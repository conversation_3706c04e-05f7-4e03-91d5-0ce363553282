# Feishu Video Downloader

A Python script to download MP4 videos from Feishu wiki pages.

## Prerequisites

- Python 3.7 or higher
- Chrome browser installed (for Selenium)
- Chrome WebDriver (compatible with your Chrome version)

## Installation

1. Install required packages:
   ```
   pip install -r requirements.txt
   ```

2. Make sure you have Chrome WebDriver installed and in your PATH, or specify the path in the script.

## Usage

1. Edit the `download_feishu_videos.py` file to set the Feishu URL and download directory:
   ```python
   feishu_url = "https://pcnkayet6fgn.feishu.cn/wiki/Ivbow7S1di4bylkV2NOcBOQdnwg"
   download_dir = "Dpan"  # Change this to your desired download location
   ```

2. Run the script:
   ```
   python download_feishu_videos.py
   ```

3. The script will:
   - Access the Feishu wiki page
   - Extract all MP4 video URLs
   - Download the videos to the specified directory
   - Show download progress

## Troubleshooting

- If you encounter authentication issues, you may need to log in to Feishu manually and then use cookies in the script
- If videos aren't found, try increasing the wait time in the `get_page_content` method
- For Feishu pages with special access requirements, you might need to customize the authentication process 