# 项目依赖库功能参考文档

本文档详细记录了AI绘本与短视频创作平台项目中使用的所有第三方库及其功能，便于后续开发时快速查找和使用。

## 📋 目录

- [Web框架和API](#web框架和api)
- [AI/LLM相关库](#aillm相关库)
- [数据库和存储](#数据库和存储)
- [文档和文件处理](#文档和文件处理)
- [图像和多媒体处理](#图像和多媒体处理)
- [网络和爬虫](#网络和爬虫)
- [数据处理和分析](#数据处理和分析)
- [认证和安全](#认证和安全)
- [任务队列和调度](#任务队列和调度)
- [测试和开发工具](#测试和开发工具)
- [监控和日志](#监控和日志)
- [工具库](#工具库)

---

## Web框架和API

### FastAPI (`fastapi>=0.95.0`)
- **功能**: 现代、快速的Web框架，用于构建API
- **用途**: 项目的主要Web框架
- **特性**: 自动API文档、类型检查、异步支持
- **使用场景**: 构建RESTful API、WebSocket支持

### Uvicorn (`uvicorn>=0.21.1`)
- **功能**: ASGI服务器，用于运行FastAPI应用
- **用途**: 生产环境部署
- **特性**: 高性能、支持异步
- **使用场景**: 启动Web服务

### Starlette相关
- **sse-starlette** (`>=1.8.0`): Server-Sent Events支持
- **websockets** (`>=12.0`): WebSocket协议支持
- **用途**: 实时通信、流式响应

---

## AI/LLM相关库

### LangChain生态系统
#### LangChain Core (`langchain>=0.1.0`)
- **功能**: LLM应用开发框架
- **用途**: 构建AI Agent、链式调用、Prompt管理
- **核心组件**: 
  - Chains: 链式调用
  - Agents: 智能体
  - Memory: 对话记忆
  - Prompts: 提示词模板

#### LangChain Community (`langchain-community>=0.0.20`)
- **功能**: 第三方集成和社区贡献组件
- **包含内容**:
  - 各种LLM提供者集成
  - 向量数据库连接器
  - 文档加载器
  - 工具包装器
- **使用场景**: 集成外部服务、扩展功能

#### LangChain Core (`langchain-core>=0.1.0`)
- **功能**: LangChain的核心抽象和接口
- **用途**: 基础组件、类型定义

### LLM提供者SDK
#### DashScope (`dashscope>=1.14.0`)
- **功能**: 阿里云通义千问官方SDK
- **用途**: 调用通义千问API
- **支持功能**: 文本生成、多模态、函数调用

#### OpenAI (`openai>=1.12.0`)
- **功能**: OpenAI官方SDK
- **用途**: 调用GPT系列模型
- **支持功能**: 文本生成、图像生成、语音处理

### 向量数据库和嵌入
#### ChromaDB (`chromadb>=0.4.0`)
- **功能**: 开源向量数据库
- **用途**: 存储和检索文档嵌入
- **特性**: 轻量级、易部署、支持过滤

#### FAISS (`faiss-cpu>=1.7.4`)
- **功能**: Facebook AI相似性搜索库
- **用途**: 高效向量相似性搜索
- **特性**: 高性能、支持大规模数据

#### Sentence Transformers (`sentence-transformers>=2.2.0`)
- **功能**: 句子和文档嵌入模型
- **用途**: 生成文本嵌入向量
- **支持模型**: BERT、RoBERTa等预训练模型

---

## 数据库和存储

### 关系型数据库
#### SQLAlchemy (`sqlalchemy>=2.0.9`)
- **功能**: Python SQL工具包和ORM
- **用途**: 数据库操作、模型定义
- **特性**: 支持多种数据库、异步支持

#### Alembic (`alembic>=1.10.3`)
- **功能**: SQLAlchemy的数据库迁移工具
- **用途**: 管理数据库结构变更
- **特性**: 版本控制、自动迁移脚本

#### 数据库驱动
- **psycopg2-binary** (`>=2.9.6`): PostgreSQL驱动
- **aiosqlite** (`>=0.19.0`): 异步SQLite驱动
- **asyncpg** (`>=0.29.0`): 异步PostgreSQL驱动

#### SQLAlchemy工具
- **sqlalchemy-utils** (`>=0.41.0`): SQLAlchemy扩展工具
- **功能**: 数据类型扩展、工具函数

### NoSQL和缓存
#### Redis (`redis>=4.5.4`)
- **功能**: 内存数据库和缓存
- **用途**: 缓存、会话存储、任务队列
- **特性**: 高性能、支持多种数据结构

#### 缓存工具
- **diskcache** (`>=5.6.0`): 磁盘缓存
- **cachetools** (`>=5.3.0`): 内存缓存工具

### 云存储
#### AWS SDK (`boto3>=1.34.0`)
- **功能**: Amazon Web Services SDK
- **用途**: S3对象存储、其他AWS服务
- **特性**: 完整的AWS服务支持

#### MinIO (`minio>=7.2.0`)
- **功能**: 对象存储客户端
- **用途**: 私有云存储、兼容S3 API
- **特性**: 轻量级、易部署

---

## 文档和文件处理

### PDF处理
#### PyPDF (`pypdf>=3.17.0`)
- **功能**: PDF文档读取和处理
- **用途**: 提取PDF文本、合并分割PDF
- **特性**: 纯Python实现、无外部依赖

### Office文档
#### python-docx (`python-docx>=0.8.11`)
- **功能**: Word文档处理
- **用途**: 读取和创建.docx文件
- **特性**: 支持文本、表格、图片

#### openpyxl (`openpyxl>=3.1.0`)
- **功能**: Excel文档处理
- **用途**: 读取和创建.xlsx文件
- **特性**: 支持公式、图表、样式

### 文本格式
#### Markdown (`markdown>=3.5.0`)
- **功能**: Markdown文档处理
- **用途**: 解析和生成Markdown
- **特性**: 扩展支持、HTML转换

#### Unstructured (`unstructured>=0.10.0`)
- **功能**: 非结构化文档处理
- **用途**: 处理各种文档格式
- **支持格式**: PDF、Word、PowerPoint、HTML等

---

## 图像和多媒体处理

### 图像处理
#### Pillow (`pillow>=10.0.0`)
- **功能**: Python图像处理库
- **用途**: 图像读取、编辑、格式转换
- **特性**: 支持多种图像格式、滤镜效果

#### OpenCV (`opencv-python>=4.8.0`)
- **功能**: 计算机视觉库
- **用途**: 图像处理、视频处理、机器视觉
- **特性**: 高性能、丰富的算法

### 音频处理
#### PyDub (`pydub>=0.25.1`)
- **功能**: 音频文件处理
- **用途**: 音频格式转换、剪辑、合并
- **特性**: 简单易用、支持多种格式

#### SpeechRecognition (`speechrecognition>=3.10.0`)
- **功能**: 语音识别
- **用途**: 语音转文字
- **支持引擎**: Google、百度、讯飞等

---

## 网络和爬虫

### HTTP客户端
#### Requests (`requests>=2.31.0`)
- **功能**: HTTP库
- **用途**: 发送HTTP请求
- **特性**: 简单易用、功能完整

#### HTTPX (`httpx>=0.24.0`)
- **功能**: 现代HTTP客户端
- **用途**: 同步和异步HTTP请求
- **特性**: HTTP/2支持、类型提示

#### aiohttp (`aiohttp>=3.8.0`)
- **功能**: 异步HTTP客户端/服务器
- **用途**: 异步网络请求
- **特性**: 高性能、WebSocket支持

### 网络爬虫
#### Scrapy (`scrapy>=2.11.0`)
- **功能**: 网络爬虫框架
- **用途**: 大规模数据抓取
- **特性**: 分布式、中间件支持

#### BeautifulSoup (`beautifulsoup4>=4.12.2`)
- **功能**: HTML/XML解析器
- **用途**: 网页内容提取
- **特性**: 容错性强、API友好

#### Selenium (`selenium>=4.15.2`)
- **功能**: Web自动化工具
- **用途**: 动态网页抓取、自动化测试
- **特性**: 支持JavaScript、多浏览器

### API工具
#### Wikipedia (`wikipedia>=1.4.0`)
- **功能**: Wikipedia API客户端
- **用途**: 获取Wikipedia内容
- **特性**: 简单易用、支持多语言

---

## 数据处理和分析

### 数据科学库
#### Pandas (`pandas>=2.0.0`)
- **功能**: 数据分析和处理库
- **用途**: 数据清洗、分析、转换
- **特性**: DataFrame、时间序列、统计分析

#### NumPy (`numpy>=1.24.0`)
- **功能**: 数值计算库
- **用途**: 数组操作、数学运算
- **特性**: 高性能、广泛支持

#### Scikit-learn (`scikit-learn>=1.3.0`)
- **功能**: 机器学习库
- **用途**: 分类、回归、聚类、降维
- **特性**: 易用、文档完善

### 自然语言处理
#### Jieba (`jieba>=0.42.1`)
- **功能**: 中文分词库
- **用途**: 中文文本分词、词性标注
- **特性**: 支持自定义词典、多种分词模式

#### NLTK (`nltk>=3.8.0`)
- **功能**: 自然语言处理工具包
- **用途**: 文本处理、语言分析
- **特性**: 丰富的语料库、多种算法

#### spaCy (`spacy>=3.7.0`)
- **功能**: 高级NLP库
- **用途**: 命名实体识别、依存分析
- **特性**: 高性能、生产就绪

---

## 认证和安全

### JWT和认证
#### PyJWT (`pyjwt>=2.6.0`)
- **功能**: JSON Web Token实现
- **用途**: 用户认证、API令牌
- **特性**: 安全、标准化

#### python-jose (`python-jose>=3.3.0`)
- **功能**: JOSE标准实现
- **用途**: JWT、JWS、JWE处理
- **特性**: 加密支持、多算法

#### passlib (`passlib>=1.7.4`)
- **功能**: 密码哈希库
- **用途**: 密码加密、验证
- **支持算法**: bcrypt、scrypt、argon2

### 加密和安全
#### cryptography (`cryptography>=40.0.1`)
- **功能**: 加密算法库
- **用途**: 数据加密、数字签名
- **特性**: 高级加密、安全随机数

#### python-multipart (`python-multipart>=0.0.6`)
- **功能**: 多部分表单数据解析
- **用途**: 文件上传处理
- **特性**: 流式处理、内存优化

---

## 任务队列和调度

### 任务调度
#### APScheduler (`apscheduler>=3.10.0`)
- **功能**: 高级Python调度器
- **用途**: 定时任务、周期性任务
- **特性**: 多种触发器、持久化支持

#### RQ (`rq>=1.15.0`)
- **功能**: Redis队列
- **用途**: 后台任务处理
- **特性**: 简单易用、监控界面

#### Croniter (`croniter>=1.4.0`)
- **功能**: Cron表达式解析
- **用途**: 解析和计算Cron时间
- **特性**: 完整的Cron支持

---

## 测试和开发工具

### 测试框架
#### Pytest (`pytest>=7.2.2`)
- **功能**: Python测试框架
- **用途**: 单元测试、集成测试
- **特性**: 插件丰富、断言清晰

#### pytest-asyncio (`pytest-asyncio>=0.21.0`)
- **功能**: 异步测试支持
- **用途**: 测试异步代码
- **特性**: 与pytest集成

#### pytest-mock (`pytest-mock>=3.11.0`)
- **功能**: Mock测试工具
- **用途**: 模拟对象、隔离测试
- **特性**: 简化Mock使用

#### Responses (`responses>=0.23.0`)
- **功能**: HTTP响应模拟
- **用途**: 模拟HTTP请求响应
- **特性**: 测试网络代码

### 代码质量
#### Black (`black>=23.3.0`)
- **功能**: Python代码格式化工具
- **用途**: 统一代码风格
- **特性**: 零配置、确定性格式化

#### isort (`isort>=5.12.0`)
- **功能**: import语句排序
- **用途**: 整理导入语句
- **特性**: 可配置、多种风格

#### flake8 (`flake8>=6.0.0`)
- **功能**: 代码检查工具
- **用途**: 发现代码问题
- **特性**: 插件支持、可配置

---

## 监控和日志

### 日志系统
#### Loguru (`loguru>=0.7.0`)
- **功能**: 现代日志库
- **用途**: 应用日志记录
- **特性**: 简单配置、丰富格式

#### structlog (`structlog>=23.2.0`)
- **功能**: 结构化日志
- **用途**: 机器可读日志
- **特性**: JSON格式、上下文绑定

### 监控指标
#### prometheus-client (`prometheus-client>=0.19.0`)
- **功能**: Prometheus监控客户端
- **用途**: 应用指标收集
- **特性**: 标准化指标、时间序列

#### memory-profiler (`memory-profiler>=0.61.0`)
- **功能**: 内存使用分析
- **用途**: 性能调优、内存泄漏检测
- **特性**: 逐行分析、可视化

---

## 工具库

### 数据验证
#### Pydantic (`pydantic>=2.0.0`)
- **功能**: 数据验证和设置管理
- **用途**: API数据验证、配置管理
- **特性**: 类型提示、自动验证

#### pydantic-settings (`pydantic-settings>=2.0.0`)
- **功能**: Pydantic设置管理
- **用途**: 环境变量配置
- **特性**: 自动类型转换

#### Marshmallow (`marshmallow>=3.20.0`)
- **功能**: 数据序列化库
- **用途**: 对象序列化、反序列化
- **特性**: 灵活的模式定义

#### Cerberus (`cerberus>=1.3.0`)
- **功能**: 数据验证库
- **用途**: 文档验证、数据清洗
- **特性**: 声明式规则

### 配置管理
#### python-decouple (`python-decouple>=3.8`)
- **功能**: 环境变量解耦
- **用途**: 配置与代码分离
- **特性**: 类型转换、默认值

#### python-dotenv (`python-dotenv>=1.0.0`)
- **功能**: .env文件支持
- **用途**: 开发环境配置
- **特性**: 环境变量加载

### 时间处理
#### pytz (`pytz>=2023.3`)
- **功能**: 时区处理库
- **用途**: 时区转换、本地化
- **特性**: 完整的时区数据库

### 模板引擎
#### Jinja2 (`jinja2>=3.1.0`)
- **功能**: 模板引擎
- **用途**: 动态内容生成
- **特性**: 安全、可扩展

### JSON处理
#### orjson (`orjson>=3.9.0`)
- **功能**: 快速JSON库
- **用途**: JSON序列化、反序列化
- **特性**: 高性能、类型支持

### 进度显示
#### tqdm (`tqdm>=4.66.0`)
- **功能**: 进度条库
- **用途**: 显示处理进度
- **特性**: 多种样式、自动估算

#### Rich (`rich>=13.0.0`)
- **功能**: 富文本和美化输出
- **用途**: 控制台美化、进度条
- **特性**: 表格、语法高亮、进度条

#### PrettyTable (`prettytable>=3.9.0`)
- **功能**: 表格显示库
- **用途**: 格式化表格输出
- **特性**: 多种样式、对齐选项

### 命令行工具
#### Click (`click>=8.1.0`)
- **功能**: 命令行界面创建
- **用途**: CLI应用开发
- **特性**: 装饰器语法、自动帮助

---

## 📚 使用建议

### 按功能分类快速查找

1. **需要处理文档时**: 查看"文档和文件处理"部分
2. **需要AI功能时**: 查看"AI/LLM相关库"部分
3. **需要数据库操作时**: 查看"数据库和存储"部分
4. **需要网络请求时**: 查看"网络和爬虫"部分
5. **需要图像处理时**: 查看"图像和多媒体处理"部分

### 常用组合

- **Web API开发**: FastAPI + SQLAlchemy + Pydantic + Alembic
- **AI对话系统**: LangChain + DashScope/OpenAI + ChromaDB
- **文档处理**: PyPDF + python-docx + unstructured
- **数据分析**: Pandas + NumPy + Scikit-learn
- **测试开发**: Pytest + pytest-mock + responses

### 版本管理

- 所有库都指定了最低版本要求
- 建议定期更新到最新稳定版本
- 生产环境建议锁定具体版本号

---

**最后更新**: 2025-07-26
**维护者**: 项目开发团队
**用途**: 快速查找和了解项目中使用的第三方库功能
