# 项目依赖安装指南

本文档提供了项目依赖库的分层安装指南，避免依赖冲突问题。

## 🎯 安装策略

我们将依赖库分为三个层次，按需安装：

1. **核心库** - 项目运行必需
2. **AI库** - LLM功能测试需要  
3. **扩展库** - 特定功能需要

## 📦 分层安装

### 第一步：安装核心库（必需）

```bash
# 安装项目运行的核心依赖
pip install -r requirements-core.txt
```

**包含内容**：
- Web框架：FastAPI、Uvicorn、Pydantic
- 数据库：SQLAlchemy、Alembic、数据库驱动
- 认证：JWT、密码哈希
- 基础工具：日志、配置、测试

### 第二步：安装AI库（测试LLM时需要）

```bash
# 仅在测试LLM功能时安装
pip install -r requirements-ai.txt
```

**包含内容**：
- LangChain核心库
- 通义千问SDK

### 第三步：按需安装扩展库

```bash
# 根据具体需求选择安装

# 数据处理
pip install numpy pandas

# 向量数据库
pip install chromadb

# 图像处理
pip install pillow opencv-python

# 文档处理
pip install pypdf python-docx openpyxl

# 开发工具
pip install black isort flake8
```

## 🚀 快速开始

### 最小化安装（推荐）

如果您只想运行基础功能和测试：

```bash
# 1. 安装核心库
pip install -r requirements-core.txt

# 2. 验证安装
python -c "import fastapi, sqlalchemy, pydantic; print('核心库安装成功')"
```

### 完整安装

如果您需要所有功能：

```bash
# 1. 核心库
pip install -r requirements-core.txt

# 2. AI功能
pip install -r requirements-ai.txt

# 3. 数据处理
pip install numpy pandas

# 4. 测试工具
pip install tqdm prettytable
```

## 🔧 故障排除

### 依赖冲突解决

如果遇到 `resolution-too-deep` 错误：

1. **清理环境**：
   ```bash
   pip uninstall -y -r requirements.txt
   ```

2. **分步安装**：
   ```bash
   pip install fastapi uvicorn pydantic
   pip install sqlalchemy alembic
   pip install redis loguru python-dotenv
   ```

3. **使用虚拟环境**：
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # 或
   venv\Scripts\activate     # Windows
   ```

### 常见问题

1. **psycopg2安装失败**：
   ```bash
   # 使用二进制版本
   pip install psycopg2-binary
   ```

2. **cryptography安装失败**：
   ```bash
   # 升级pip和setuptools
   pip install --upgrade pip setuptools
   pip install cryptography
   ```

3. **langchain-community依赖复杂**：
   ```bash
   # 单独安装，指定版本
   pip install langchain==0.1.0
   pip install langchain-community==0.0.20
   ```

## 📋 验证安装

### 核心功能验证

```python
# test_installation.py
try:
    import fastapi
    import sqlalchemy
    import pydantic
    import redis
    import loguru
    print("✅ 核心库安装成功")
except ImportError as e:
    print(f"❌ 核心库缺失: {e}")

try:
    from jose import jwt
    from passlib.context import CryptContext
    print("✅ 认证库安装成功")
except ImportError as e:
    print(f"❌ 认证库缺失: {e}")
```

### AI功能验证

```python
# test_ai_installation.py
try:
    import langchain
    import dashscope
    print("✅ AI库安装成功")
except ImportError as e:
    print(f"❌ AI库缺失: {e}")
```

## 🎯 推荐安装顺序

1. **开发阶段**：只安装 `requirements-core.txt`
2. **测试LLM**：再安装 `requirements-ai.txt`
3. **生产部署**：根据实际功能需求选择性安装

## 📝 版本管理

- 所有核心库都指定了最低版本
- AI库使用最新稳定版本
- 生产环境建议锁定具体版本：
  ```bash
  pip freeze > requirements-lock.txt
  ```

---

**最后更新**: 2025-07-26  
**维护者**: 项目开发团队
