# Python 标准库导入
import os
import sys
from pathlib import Path

# 第三方库导入
import uvicorn
from loguru import logger

from app.core.database import init_db, drop_db

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent
sys.path.append(str(ROOT_DIR))

# 本地模块导入
from app.core.config import settings

def check_environment():
    """检查运行环境"""
    missing_vars = []
    
    # 检查数据库配置
    if not settings.DATABASE_URL:
        missing_vars.append("DATABASE_URL")
    
    # 检查JWT配置
    if not settings.SECRET_KEY:
        missing_vars.append("SECRET_KEY")
    if not settings.ALGORITHM:
        missing_vars.append("ALGORITHM")
    
    # 检查AES加密配置
    if not settings.AES_KEY:
        missing_vars.append("AES_KEY")
    if not settings.AES_IV:
        missing_vars.append("AES_IV")
    
    # 检查AI服务配置（至少需要一个）
    if not settings.OPENAI_API_KEY and not settings.QIANWEN_API_KEY:
        missing_vars.append("OPENAI_API_KEY 或 QIANWEN_API_KEY")
    
    if missing_vars:
        logger.error("缺少必要的环境变量: {}", ", ".join(set(missing_vars)))
        logger.info("\n请确保已正确配置以下环境变量：")
        logger.info("1. 数据库配置：")
        logger.info("   - DATABASE_URL: 数据库连接URL")
        logger.info("\n2. JWT配置：")
        logger.info("   - SECRET_KEY: JWT密钥")
        logger.info("   - ALGORITHM: JWT算法（默认HS256）")
        logger.info("\n3. AES加密配置：")
        logger.info("   - AES_KEY: 32字节的AES密钥")
        logger.info("   - AES_IV: 16字节的初始化向量")
        logger.info("\n4. AI服务配置（至少配置一个）：")
        logger.info("   - OPENAI_API_KEY: OpenAI API密钥")
        logger.info("   - QIANWEN_API_KEY: 千问API密钥")
        sys.exit(1)

def init_application():
    """初始化应用"""
    # 检查环境
    check_environment()
    
    # 初始化数据库
    if settings.INIT_DB:
        logger.info("初始化数据库...")
        # drop_db()
        init_db()
        logger.info("数据库初始化完成！")
    
    # 打印环境信息
    logger.info("当前运行环境: {}", settings.ENVIRONMENT)
    logger.info("调试模式: {}", "开启" if settings.DEBUG else "关闭")
    logger.info("API版本: {}", settings.VERSION)
    logger.info("API路径: {}", settings.API_V1_STR)
    logger.info("加密功能: {}", "开启" if settings.AES_ENCRYPTION_ENABLED else "关闭")
    logger.info("AI服务: {}", settings.DEFAULT_AI_SERVICE)

def main():
    """启动应用"""
    # 初始化应用
    init_application()
    
    # 配置 uvicorn
    uvicorn_config = {
        "app": "app.main:app",
        "host": settings.HOST,
        "port": settings.PORT,
        "reload": settings.DEBUG,
        "log_level": settings.LOG_LEVEL.lower()
    }
    
    # 只在非调试模式下使用 workers
    if not settings.DEBUG:
        uvicorn_config["workers"] = settings.WORKERS
    
    # 启动应用
    logger.info("启动服务器: {}:{}", settings.HOST, settings.PORT)
    uvicorn.run(**uvicorn_config)

if __name__ == "__main__":
    main() 