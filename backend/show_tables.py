"""
显示SQLAlchemy元数据中包含的所有表
"""

# 确保导入所有模型
from app.core.database.models.user import User, AuthProvider
from app.core.database.models.token import Token
from app.core.database.base import Base
from loguru import logger

def main():
    """显示元数据中的表信息"""
    logger.info("Base.metadata包含的表:")
    
    for table_name, table in Base.metadata.tables.items():
        logger.info(f"表名: {table_name}")
        logger.info(f"  - 列: {', '.join(column.name for column in table.columns)}")
        if table.foreign_keys:
            logger.info(f"  - 外键: {', '.join(str(fk) for fk in table.foreign_keys)}")
        if table.indexes:
            logger.info(f"  - 索引: {', '.join(str(idx) for idx in table.indexes)}")
        logger.info("---")

if __name__ == "__main__":
    main() 