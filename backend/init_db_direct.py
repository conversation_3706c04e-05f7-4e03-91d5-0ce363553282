"""
直接使用SQLite连接创建表
"""

import os
import sqlite3
from loguru import logger
from app.core.config import settings

def create_tables(db_path):
    """直接使用SQLite连接创建必要的表"""
    logger.info(f"使用路径创建数据库表: {db_path}")
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(db_path)), exist_ok=True)
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON;")
        
        # 创建users表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            nickname TEXT,
            hashed_password TEXT,
            full_name TEXT,
            is_active BOOLEAN DEFAULT 1,
            is_superuser BOOLEAN DEFAULT 0,
            auth_provider TEXT DEFAULT 'local',
            provider_id TEXT UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP
        );
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS ix_users_username ON users (username);')
        cursor.execute('CREATE INDEX IF NOT EXISTS ix_users_email ON users (email);')
        
        # 创建tokens表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            access_token TEXT UNIQUE NOT NULL,
            refresh_token TEXT UNIQUE,
            is_active BOOLEAN DEFAULT 1,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        );
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS ix_tokens_access_token ON tokens (access_token);')
        cursor.execute('CREATE INDEX IF NOT EXISTS ix_tokens_refresh_token ON tokens (refresh_token);')
        
        # 提交更改
        conn.commit()
        logger.success("数据库表创建成功!")
        
        # 检查表是否创建成功
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        logger.info(f"数据库中的表: {', '.join(table[0] for table in tables)}")
        
        # 关闭连接
        conn.close()
        logger.info("数据库连接已关闭")
        
    except Exception as e:
        logger.error(f"创建表失败: {str(e)}")
        raise

def main():
    """主函数"""
    # 获取SQLite数据库路径
    db_url = settings.DATABASE_URL
    
    if db_url.startswith("sqlite:///"):
        # 绝对路径
        db_path = db_url[10:]
    elif db_url.startswith("sqlite://"):
        # 相对路径
        db_path = db_url[9:]
    else:
        logger.error(f"不是SQLite URL: {db_url}")
        return
    
    # 创建表
    create_tables(db_path)

if __name__ == "__main__":
    main() 