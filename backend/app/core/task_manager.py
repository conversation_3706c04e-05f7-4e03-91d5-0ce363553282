from concurrent.futures import Future
from app.core.thread_pool import parse_thread_pool
from typing import Dict, Any, Optional, Callable
from loguru import logger

class ThreadTaskManager:
    """多线程任务管理器，用于控制并发任务执行"""
    def __init__(self, max_workers: int = 5):
        self.max_workers = max_workers
        self.tasks: Dict[str, Future] = {}  # 任务字典，键为任务ID，值为future对象
        logger.info(f"多线程任务管理器初始化，最大并发任务数: {max_workers}")

    def submit_task(
        self,
        task_id: str,
        task_type: str,
        func: Callable,
        *args,
        **kwargs
    ) -> bool:
        """
        提交任务到解析线程池
        Args:
            task_id: 任务ID，必须唯一
            task_type: 任务类型
            func: 要执行的函数
            *args, **kwargs: 函数参数
        Returns:
            bool: 任务是否成功提交
        """
        if task_id in self.tasks:
            logger.warning(f"任务已存在: {task_id}")
            return False
        future = parse_thread_pool.submit(func, *args, **kwargs)
        self.tasks[task_id] = future
        logger.info(f"任务已提交: ID={task_id}, 类型={task_type}")
        return True

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        Args:
            task_id: 任务ID
        Returns:
            Optional[Dict[str, Any]]: 任务信息
        """
        future = self.tasks.get(task_id)
        if not future:
            return None
        if future.done():
            try:
                result = future.result()
                return {"status": "completed", "result": result}
            except Exception as e:
                return {"status": "failed", "error": str(e)}
        else:
            return {"status": "running"}

    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有任务信息
        Returns:
            Dict[str, Dict[str, Any]]: 所有任务信息
        """
        all_status = {}
        for task_id, future in self.tasks.items():
            if future.done():
                try:
                    result = future.result()
                    all_status[task_id] = {"status": "completed", "result": result}
                except Exception as e:
                    all_status[task_id] = {"status": "failed", "error": str(e)}
            else:
                all_status[task_id] = {"status": "running"}
        return all_status

    def get_queue_length(self) -> int:
        """
        获取队列长度（未完成的任务数）
        Returns:
            int: 等待或运行中的任务数量
        """
        return sum(1 for f in self.tasks.values() if not f.done())

    def get_running_tasks_count(self) -> int:
        """
        获取正在运行的任务数量（近似）
        Returns:
            int: 运行中的任务数量
        """
        return self.get_queue_length()

# 创建全局任务管理器实例
# 默认最多同时运行5个任务
task_manager = ThreadTaskManager(max_workers=5) 