"""
LLM Core Module
提供LLM相关的核心功能 - 统一新版本
支持"万物皆Agent"的垂直架构
"""

# 导入核心组件
from .llm_providers import get_llm_provider, get_qwen_llm

# 导入新的Agent架构组件
from .agent_factory import create_agent, get_available_agents, get_agent_info, AgentConfig
from .base import BaseAgent, BaseTool, BaseMemory, BasePrompt

# 导入Agent客户端
from .agent_client import (
    AgentClient,
    MultiAgentClient,
    EnhancedAgentClient,
    create_agent_client,
    create_multi_agent_client,
    get_available_agent_types
)

# 导入垂直架构Agent
try:
    from .agents import ChapterSplitAgent
except ImportError:
    ChapterSplitAgent = None

__all__ = [
    # 核心组件
    'get_llm_provider',
    'get_qwen_llm',

    # Agent客户端
    'AgentClient',
    'MultiAgentClient',
    'EnhancedAgentClient',
    'create_agent_client',
    'create_multi_agent_client',
    'get_available_agent_types',

    # 垂直Agent架构
    'create_agent',
    'get_available_agents',
    'get_agent_info',
    'AgentConfig',
    'BaseAgent',
    'BaseTool',
    'BaseMemory',
    'BasePrompt',

    # 专业Agent
    'ChapterSplitAgent'
]
