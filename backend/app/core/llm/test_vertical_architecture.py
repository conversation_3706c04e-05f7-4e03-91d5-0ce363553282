"""
垂直架构测试文件
验证"万物皆Agent"垂直架构的功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_file = Path(__file__).resolve()
project_root = current_file.parents[4]  # 向上4级到项目根目录
sys.path.insert(0, str(project_root))

from loguru import logger

# 导入新架构组件
from backend.app.core.llm import (
    create_agent, 
    get_available_agents, 
    get_agent_info,
    AgentConfig,
    BaseAgent
)


def test_agent_factory():
    """测试Agent工厂功能"""
    print("\n=== 测试Agent工厂 ===")
    
    try:
        # 获取可用Agent
        available_agents = get_available_agents()
        print(f"✓ 可用Agent类型: {available_agents}")
        
        # 测试Agent信息获取
        for agent_type in available_agents:
            info = get_agent_info(agent_type)
            print(f"✓ {agent_type} Agent信息: {info}")
            
        return True
        
    except Exception as e:
        print(f"✗ Agent工厂测试失败: {e}")
        return False


def test_chapter_split_agent_creation():
    """测试章节分割Agent创建"""
    print("\n=== 测试章节分割Agent创建 ===")
    
    try:
        # 创建Agent配置
        config = AgentConfig(
            agent_type="chapter_split",
            llm_provider="qwen",
            verbose=True
        )
        
        # 创建Agent
        agent = create_agent("chapter_split", config)
        print(f"✓ 成功创建Agent: {type(agent).__name__}")
        
        # 获取Agent信息
        info = agent.get_agent_info()
        print(f"✓ Agent信息: {info}")
        
        # 获取能力列表
        capabilities = agent.get_capabilities()
        print(f"✓ Agent能力: {capabilities}")
        
        return agent
        
    except Exception as e:
        print(f"✗ 章节分割Agent创建失败: {e}")
        return None


def test_agent_tools():
    """测试Agent专用工具"""
    print("\n=== 测试Agent专用工具 ===")
    
    try:
        # 创建Agent
        config = AgentConfig(agent_type="chapter_split", verbose=False)
        agent = create_agent("chapter_split", config)
        
        # 测试正则工具
        regex_result = agent.regex_tool.execute(
            operation="validate",
            pattern=r"第\d+章.*?"
        )
        print(f"✓ 正则验证结果: {regex_result}")
        
        # 测试模式分析工具
        test_text = """
        第一章 开始的故事
        这是第一章的内容...
        
        第二章 继续冒险
        这是第二章的内容...
        
        第三章 最终决战
        这是第三章的内容...
        """
        
        analysis_result = agent.pattern_tool.execute(
            operation="extract_titles",
            text=test_text
        )
        print(f"✓ 模式分析结果: 发现{len(analysis_result)}个潜在标题")
        
        return True
        
    except Exception as e:
        print(f"✗ Agent工具测试失败: {e}")
        return False


def test_agent_prompts():
    """测试Agent专用提示模板"""
    print("\n=== 测试Agent专用提示模板 ===")
    
    try:
        # 创建Agent
        config = AgentConfig(agent_type="chapter_split", verbose=False)
        agent = create_agent("chapter_split", config)
        
        # 测试分析提示模板
        analysis_prompt = agent.analysis_prompt.format(
            text_snippet="第一章 开始\n这是内容...",
            intro_patterns=["序章.*?"],
            regular_patterns=["第\\d+章.*?"],
            anywhere_patterns=["尾声.*?"]
        )
        print(f"✓ 分析提示模板格式化成功，长度: {len(analysis_prompt)}")
        
        # 测试评估提示模板
        test_chapters = [
            {"title": "第一章", "word_count": 3000},
            {"title": "第二章", "word_count": 2800}
        ]
        
        evaluation_prompt = agent.evaluation_prompt.format(
            chapters=test_chapters,
            original_length=5800,
            split_method="pattern"
        )
        print(f"✓ 评估提示模板格式化成功，长度: {len(evaluation_prompt)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Agent提示模板测试失败: {e}")
        return False


def test_agent_memory():
    """测试Agent专用记忆"""
    print("\n=== 测试Agent专用记忆 ===")
    
    try:
        # 创建Agent
        config = AgentConfig(agent_type="chapter_split", verbose=False)
        agent = create_agent("chapter_split", config)
        
        # 测试模式记忆
        test_patterns = {
            "intro": ["序章.*?", "前言.*?"],
            "regular": ["第\\d+章.*?", "第\\d+回.*?"],
            "anywhere": ["尾声.*?", "后记.*?"]
        }
        
        # 添加模式
        success = agent.pattern_memory.add_patterns(test_patterns)
        print(f"✓ 模式添加结果: {success}")
        
        # 获取模式
        stored_patterns = agent.pattern_memory.get_all_patterns()
        print(f"✓ 存储的模式数量: {sum(len(p) for p in stored_patterns.values())}")
        
        # 测试模式统计
        agent.pattern_memory.update_pattern_stats("第\\d+章.*?", success=True)
        effectiveness = agent.pattern_memory.get_pattern_effectiveness()
        print(f"✓ 模式有效性统计: {len(effectiveness)}个模式")
        
        return True
        
    except Exception as e:
        print(f"✗ Agent记忆测试失败: {e}")
        return False


def test_chapter_split_functionality():
    """测试章节分割核心功能"""
    print("\n=== 测试章节分割核心功能 ===")
    
    try:
        # 创建Agent
        config = AgentConfig(agent_type="chapter_split", verbose=True)
        agent = create_agent("chapter_split", config)
        
        # 测试文本
        test_content = """
        序章 起源
        很久很久以前，在一个遥远的王国里...
        这是序章的内容，大约有一千字左右的内容。
        
        第一章 英雄的诞生
        在一个普通的村庄里，住着一个名叫小明的少年...
        这是第一章的内容，描述了主角的出生和成长经历。
        内容比较丰富，大约有三千字左右。
        
        第二章 踏上冒险之路
        小明长大后，决定离开村庄去寻找传说中的宝藏...
        这是第二章的内容，讲述了主角开始冒险的故事。
        同样有着丰富的情节和描述。
        
        第三章 遇见伙伴
        在冒险的路上，小明遇到了志同道合的伙伴...
        这是第三章的内容，介绍了重要的配角人物。
        
        尾声 新的开始
        经过一番冒险，小明终于找到了宝藏...
        这是尾声部分，为整个故事画下句号。
        """
        
        # 测试智能分割
        input_data = {
            "content": test_content,
            "method": "auto",
            "force_analysis": False
        }
        
        result = agent.process(input_data)
        
        if "error" in result:
            print(f"✗ 分割失败: {result['error']}")
            return False
        
        chapters = result.get("chapters", [])
        quality_score = result.get("quality_score", 0)
        
        print(f"✓ 分割成功，章节数: {len(chapters)}")
        print(f"✓ 质量评分: {quality_score}")
        print(f"✓ 使用方法: {result.get('method_used')}")
        
        # 显示章节信息
        for i, chapter in enumerate(chapters[:3]):  # 只显示前3章
            title = chapter.get("title", f"第{i+1}章")
            word_count = chapter.get("word_count", 0)
            print(f"  - {title}: {word_count}字")
        
        if len(chapters) > 3:
            print(f"  ... 还有{len(chapters)-3}章")
        
        return True
        
    except Exception as e:
        print(f"✗ 章节分割功能测试失败: {e}")
        return False


def test_agent_chat():
    """测试Agent对话功能"""
    print("\n=== 测试Agent对话功能 ===")
    
    try:
        # 创建Agent
        config = AgentConfig(agent_type="chapter_split", verbose=False)
        agent = create_agent("chapter_split", config)
        
        # 测试对话
        response = agent.chat("你好，请介绍一下你的功能")
        print(f"✓ Agent回复: {response[:100]}...")
        
        # 测试专业对话
        response2 = agent.chat("如何分析小说的章节结构？")
        print(f"✓ 专业回复: {response2[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ Agent对话测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始垂直架构测试...")
    
    tests = [
        ("Agent工厂", test_agent_factory),
        ("Agent创建", test_chapter_split_agent_creation),
        ("Agent工具", test_agent_tools),
        ("Agent提示", test_agent_prompts),
        ("Agent记忆", test_agent_memory),
        ("章节分割功能", test_chapter_split_functionality),
        ("Agent对话", test_agent_chat)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！垂直架构工作正常！")
    else:
        print("⚠️  部分测试失败，需要检查问题")
    
    return passed == total


if __name__ == "__main__":
    run_all_tests()
