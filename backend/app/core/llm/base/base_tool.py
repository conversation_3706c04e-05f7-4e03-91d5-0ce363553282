"""
工具基类
定义Agent专用工具的接口和管理器
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Type
from pathlib import Path
import importlib.util
import inspect
from loguru import logger


class BaseTool(ABC):
    """
    工具基类
    
    所有Agent专用工具都应该继承此类
    """
    
    def __init__(self, name: str, description: str = ""):
        """
        初始化工具
        
        Args:
            name: 工具名称
            description: 工具描述
        """
        self.name = name
        self.description = description
    
    @abstractmethod
    def execute(self, **kwargs) -> Any:
        """
        执行工具功能
        
        Args:
            **kwargs: 工具参数
            
        Returns:
            执行结果
        """
        pass
    
    def validate_params(self, **kwargs) -> bool:
        """
        验证工具参数
        
        Args:
            **kwargs: 工具参数
            
        Returns:
            参数是否有效
        """
        return True
    
    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具信息"""
        return {
            "name": self.name,
            "description": self.description,
            "type": self.__class__.__name__
        }


class ToolManager:
    """
    工具管理器
    
    负责加载和管理Agent专用工具
    """
    
    def __init__(self, tools_path: Path):
        """
        初始化工具管理器
        
        Args:
            tools_path: 工具目录路径
        """
        self.tools_path = tools_path
        self.tools: Dict[str, BaseTool] = {}
    
    def load_tools(self) -> Dict[str, BaseTool]:
        """
        加载工具目录中的所有工具
        
        Returns:
            工具字典
        """
        if not self.tools_path.exists():
            logger.warning(f"工具目录不存在: {self.tools_path}")
            return {}
        
        tools = {}
        
        # 遍历工具目录中的Python文件
        for tool_file in self.tools_path.glob("*.py"):
            if tool_file.name.startswith("__"):
                continue
                
            try:
                # 动态导入工具模块
                spec = importlib.util.spec_from_file_location(
                    tool_file.stem, tool_file
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # 查找工具类
                for name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and 
                        issubclass(obj, BaseTool) and 
                        obj != BaseTool):
                        
                        # 实例化工具
                        tool_instance = obj()
                        tools[tool_instance.name] = tool_instance
                        logger.debug(f"加载工具: {tool_instance.name}")
                        
            except Exception as e:
                logger.error(f"加载工具文件失败 {tool_file}: {e}")
        
        self.tools = tools
        logger.info(f"成功加载 {len(tools)} 个工具")
        return tools
    
    def get_tool(self, name: str) -> Optional[BaseTool]:
        """
        获取指定工具
        
        Args:
            name: 工具名称
            
        Returns:
            工具实例或None
        """
        return self.tools.get(name)
    
    def execute_tool(self, name: str, **kwargs) -> Any:
        """
        执行指定工具
        
        Args:
            name: 工具名称
            **kwargs: 工具参数
            
        Returns:
            执行结果
        """
        tool = self.get_tool(name)
        if not tool:
            raise ValueError(f"工具不存在: {name}")
        
        if not tool.validate_params(**kwargs):
            raise ValueError(f"工具参数无效: {name}")
        
        return tool.execute(**kwargs)
    
    def list_tools(self) -> List[Dict[str, Any]]:
        """
        列出所有工具信息
        
        Returns:
            工具信息列表
        """
        return [tool.get_tool_info() for tool in self.tools.values()]
