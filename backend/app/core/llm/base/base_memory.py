"""
记忆基类
定义Agent专用记忆的接口和管理器
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from pathlib import Path
import json
from loguru import logger


class BaseMemory(ABC):
    """
    记忆基类
    
    所有Agent专用记忆都应该继承此类
    """
    
    def __init__(self, name: str, description: str = ""):
        """
        初始化记忆
        
        Args:
            name: 记忆名称
            description: 记忆描述
        """
        self.name = name
        self.description = description
    
    @abstractmethod
    def store(self, key: str, value: Any) -> bool:
        """
        存储记忆
        
        Args:
            key: 记忆键
            value: 记忆值
            
        Returns:
            是否存储成功
        """
        pass
    
    @abstractmethod
    def retrieve(self, key: str) -> Optional[Any]:
        """
        检索记忆
        
        Args:
            key: 记忆键
            
        Returns:
            记忆值或None
        """
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """
        清空记忆
        
        Returns:
            是否清空成功
        """
        pass
    
    def get_memory_info(self) -> Dict[str, Any]:
        """获取记忆信息"""
        return {
            "name": self.name,
            "description": self.description,
            "type": self.__class__.__name__
        }


class FileMemory(BaseMemory):
    """
    文件记忆实现
    
    将记忆存储在JSON文件中
    """
    
    def __init__(self, name: str, file_path: Path, description: str = ""):
        """
        初始化文件记忆
        
        Args:
            name: 记忆名称
            file_path: 存储文件路径
            description: 记忆描述
        """
        super().__init__(name, description)
        self.file_path = file_path
        self.data: Dict[str, Any] = {}
        self._load_from_file()
    
    def _load_from_file(self):
        """从文件加载记忆"""
        if self.file_path.exists():
            try:
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                logger.debug(f"从文件加载记忆: {self.file_path}")
            except Exception as e:
                logger.error(f"加载记忆文件失败: {e}")
                self.data = {}
    
    def _save_to_file(self):
        """保存记忆到文件"""
        try:
            self.file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            logger.debug(f"保存记忆到文件: {self.file_path}")
        except Exception as e:
            logger.error(f"保存记忆文件失败: {e}")
    
    def store(self, key: str, value: Any) -> bool:
        """存储记忆"""
        try:
            self.data[key] = value
            self._save_to_file()
            return True
        except Exception as e:
            logger.error(f"存储记忆失败: {e}")
            return False
    
    def retrieve(self, key: str) -> Optional[Any]:
        """检索记忆"""
        return self.data.get(key)
    
    def clear(self) -> bool:
        """清空记忆"""
        try:
            self.data = {}
            self._save_to_file()
            return True
        except Exception as e:
            logger.error(f"清空记忆失败: {e}")
            return False


class MemoryManager:
    """
    记忆管理器
    
    负责管理Agent专用记忆
    """
    
    def __init__(self, memory_path: Path):
        """
        初始化记忆管理器
        
        Args:
            memory_path: 记忆目录路径
        """
        self.memory_path = memory_path
        self.memories: Dict[str, BaseMemory] = {}
        self._initialize_default_memories()
    
    def _initialize_default_memories(self):
        """初始化默认记忆"""
        # 创建默认的文件记忆
        default_memory = FileMemory(
            name="default",
            file_path=self.memory_path / "default.json",
            description="默认记忆存储"
        )
        self.memories["default"] = default_memory
    
    def add_memory(self, memory: BaseMemory):
        """
        添加记忆
        
        Args:
            memory: 记忆实例
        """
        self.memories[memory.name] = memory
        logger.debug(f"添加记忆: {memory.name}")
    
    def get_memory(self, name: str) -> Optional[BaseMemory]:
        """
        获取记忆
        
        Args:
            name: 记忆名称
            
        Returns:
            记忆实例或None
        """
        return self.memories.get(name)
    
    def store(self, key: str, value: Any, memory_name: str = "default") -> bool:
        """
        存储到指定记忆
        
        Args:
            key: 记忆键
            value: 记忆值
            memory_name: 记忆名称
            
        Returns:
            是否存储成功
        """
        memory = self.get_memory(memory_name)
        if memory:
            return memory.store(key, value)
        return False
    
    def retrieve(self, key: str, memory_name: str = "default") -> Optional[Any]:
        """
        从指定记忆检索
        
        Args:
            key: 记忆键
            memory_name: 记忆名称
            
        Returns:
            记忆值或None
        """
        memory = self.get_memory(memory_name)
        if memory:
            return memory.retrieve(key)
        return None
    
    def list_memories(self) -> List[Dict[str, Any]]:
        """
        列出所有记忆信息
        
        Returns:
            记忆信息列表
        """
        return [memory.get_memory_info() for memory in self.memories.values()]
