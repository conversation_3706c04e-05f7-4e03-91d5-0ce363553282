"""
提示基类
定义Agent专用提示模板的接口和管理器
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from pathlib import Path
import json
import importlib.util
import inspect
from loguru import logger


class BasePrompt(ABC):
    """
    提示基类
    
    所有Agent专用提示模板都应该继承此类
    """
    
    def __init__(self, name: str, description: str = ""):
        """
        初始化提示模板
        
        Args:
            name: 模板名称
            description: 模板描述
        """
        self.name = name
        self.description = description
    
    @abstractmethod
    def format(self, **kwargs) -> str:
        """
        格式化提示模板
        
        Args:
            **kwargs: 模板参数
            
        Returns:
            格式化后的提示
        """
        pass
    
    def validate_params(self, **kwargs) -> bool:
        """
        验证模板参数
        
        Args:
            **kwargs: 模板参数
            
        Returns:
            参数是否有效
        """
        return True
    
    def get_prompt_info(self) -> Dict[str, Any]:
        """获取提示模板信息"""
        return {
            "name": self.name,
            "description": self.description,
            "type": self.__class__.__name__
        }


class TemplatePrompt(BasePrompt):
    """
    模板提示实现
    
    基于字符串模板的提示
    """
    
    def __init__(self, name: str, template: str, description: str = ""):
        """
        初始化模板提示
        
        Args:
            name: 模板名称
            template: 模板字符串
            description: 模板描述
        """
        super().__init__(name, description)
        self.template = template
    
    def format(self, **kwargs) -> str:
        """格式化模板"""
        try:
            return self.template.format(**kwargs)
        except KeyError as e:
            logger.error(f"模板参数缺失: {e}")
            return self.template
        except Exception as e:
            logger.error(f"模板格式化失败: {e}")
            return self.template


class PromptManager:
    """
    提示管理器
    
    负责加载和管理Agent专用提示模板
    """
    
    def __init__(self, prompts_path: Path):
        """
        初始化提示管理器
        
        Args:
            prompts_path: 提示目录路径
        """
        self.prompts_path = prompts_path
        self.prompts: Dict[str, BasePrompt] = {}
    
    def load_prompts(self) -> Dict[str, BasePrompt]:
        """
        加载提示目录中的所有提示模板
        
        Returns:
            提示模板字典
        """
        if not self.prompts_path.exists():
            logger.warning(f"提示目录不存在: {self.prompts_path}")
            return {}
        
        prompts = {}
        
        # 加载JSON格式的提示模板
        for json_file in self.prompts_path.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for prompt_name, prompt_data in data.items():
                    template = prompt_data.get("template", "")
                    description = prompt_data.get("description", "")
                    
                    prompt_instance = TemplatePrompt(
                        name=prompt_name,
                        template=template,
                        description=description
                    )
                    prompts[prompt_name] = prompt_instance
                    logger.debug(f"加载JSON提示模板: {prompt_name}")
                    
            except Exception as e:
                logger.error(f"加载提示JSON文件失败 {json_file}: {e}")
        
        # 加载Python格式的提示模板
        for py_file in self.prompts_path.glob("*.py"):
            if py_file.name.startswith("__"):
                continue
                
            try:
                # 动态导入提示模块
                spec = importlib.util.spec_from_file_location(
                    py_file.stem, py_file
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # 查找提示类
                for name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and 
                        issubclass(obj, BasePrompt) and 
                        obj != BasePrompt and
                        obj != TemplatePrompt):
                        
                        # 实例化提示模板
                        prompt_instance = obj()
                        prompts[prompt_instance.name] = prompt_instance
                        logger.debug(f"加载Python提示模板: {prompt_instance.name}")
                        
            except Exception as e:
                logger.error(f"加载提示Python文件失败 {py_file}: {e}")
        
        self.prompts = prompts
        logger.info(f"成功加载 {len(prompts)} 个提示模板")
        return prompts
    
    def get_prompt(self, name: str) -> Optional[BasePrompt]:
        """
        获取指定提示模板
        
        Args:
            name: 模板名称
            
        Returns:
            提示模板实例或None
        """
        return self.prompts.get(name)
    
    def format_prompt(self, name: str, **kwargs) -> str:
        """
        格式化指定提示模板
        
        Args:
            name: 模板名称
            **kwargs: 模板参数
            
        Returns:
            格式化后的提示
        """
        prompt = self.get_prompt(name)
        if not prompt:
            raise ValueError(f"提示模板不存在: {name}")
        
        if not prompt.validate_params(**kwargs):
            raise ValueError(f"提示模板参数无效: {name}")
        
        return prompt.format(**kwargs)
    
    def list_prompts(self) -> List[Dict[str, Any]]:
        """
        列出所有提示模板信息
        
        Returns:
            提示模板信息列表
        """
        return [prompt.get_prompt_info() for prompt in self.prompts.values()]
