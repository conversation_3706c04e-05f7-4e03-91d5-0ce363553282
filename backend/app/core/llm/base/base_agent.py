"""
Agent基类
定义所有Agent的通用接口和行为
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Type
from dataclasses import dataclass, field
from pathlib import Path
from loguru import logger

from .base_tool import ToolManager
from .base_memory import MemoryManager
from .base_prompt import PromptManager


@dataclass
class AgentConfig:
    """Agent配置类"""
    agent_type: str
    llm_provider: str = "qwen"
    memory_type: str = "conversation"
    verbose: bool = True
    tools_enabled: bool = True
    custom_config: Dict[str, Any] = field(default_factory=dict)


class BaseAgent(ABC):
    """
    Agent基类
    
    所有专业Agent都应该继承此类，实现统一的接口
    """
    
    def __init__(self, config: AgentConfig, **kwargs):
        """
        初始化Agent
        
        Args:
            config: Agent配置
            **kwargs: 额外参数
        """
        self.config = config
        self.agent_type = config.agent_type
        self.verbose = config.verbose
        
        # 创建核心LLM提供者（Agent自己决定LLM）
        from ..llm_providers import get_llm_provider
        self.llm = get_llm_provider(config.llm_provider)

        # 简化的对话历史记忆
        self.conversation_history = []
        
        # 初始化管理器
        self.tool_manager = ToolManager(self._get_agent_path() / "tools")
        self.memory_manager = MemoryManager(self._get_agent_path() / "memory")
        self.prompt_manager = PromptManager(self._get_agent_path() / "prompts")
        
        # 加载Agent专用组件
        self._load_agent_components()
        
        logger.info(f"{self.agent_type} Agent初始化完成")
    
    def _get_agent_path(self) -> Path:
        """获取Agent的根目录路径"""
        current_file = Path(__file__).resolve()
        llm_root = current_file.parent.parent
        return llm_root / "agents" / self.agent_type.lower().replace("_", "_")
    
    def _load_agent_components(self):
        """加载Agent专用组件"""
        try:
            # 加载专用工具
            if self.config.tools_enabled:
                self.tools = self.tool_manager.load_tools()
                if self.verbose:
                    logger.info(f"加载了 {len(self.tools)} 个专用工具")
            
            # 加载专用提示模板
            self.prompts = self.prompt_manager.load_prompts()
            if self.verbose:
                logger.info(f"加载了 {len(self.prompts)} 个专用提示模板")
                
        except Exception as e:
            logger.warning(f"加载Agent组件时出现警告: {e}")
    
    @abstractmethod
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据的主要方法
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        pass
    
    def chat(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        与Agent对话
        
        Args:
            message: 用户消息
            context: 上下文信息
            
        Returns:
            Agent回复
        """
        # 构建增强的提示
        enhanced_prompt = self._build_enhanced_prompt(message, context)

        # 使用LLM直接处理
        try:
            if hasattr(self.llm, 'invoke'):
                response = self.llm.invoke(enhanced_prompt)
                if hasattr(response, 'content'):
                    reply = response.content
                else:
                    reply = str(response)
            else:
                reply = str(self.llm(enhanced_prompt))

            # 保存到对话历史
            self.conversation_history.append({"user": message, "ai": reply})

            # 只保留最近10轮对话
            if len(self.conversation_history) > 10:
                self.conversation_history = self.conversation_history[-10:]

            return reply

        except Exception as e:
            logger.error(f"Agent对话失败: {e}")
            return f"抱歉，处理您的请求时出现错误: {str(e)}"
    
    def _build_enhanced_prompt(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """构建增强的提示"""
        # 基础提示
        base_prompt = f"作为{self.agent_type}专家，请处理以下请求：\n{message}"
        
        # 添加上下文
        if context:
            context_str = "\n".join([f"{k}: {v}" for k, v in context.items()])
            base_prompt = f"上下文信息：\n{context_str}\n\n{base_prompt}"
        
        return base_prompt
    
    def get_capabilities(self) -> List[str]:
        """获取Agent能力列表"""
        return [
            f"{self.agent_type}专业处理",
            "智能对话交互",
            "上下文记忆管理",
            "专用工具集成"
        ]
    
    def get_agent_info(self) -> Dict[str, Any]:
        """获取Agent详细信息"""
        return {
            "agent_type": self.agent_type,
            "llm_provider": self.config.llm_provider,
            "memory_type": self.config.memory_type,
            "tools_count": len(getattr(self, 'tools', [])),
            "prompts_count": len(getattr(self, 'prompts', {})),
            "capabilities": self.get_capabilities(),
            "llm_info": {
                "llm_type": type(self.llm).__name__,
                "provider": self.config.llm_provider
            },
            "conversation_count": len(self.conversation_history)
        }

    def reset_memory(self):
        """重置Agent记忆"""
        self.conversation_history = []
        logger.info(f"{self.agent_type} Agent记忆已重置")

    def get_memory_summary(self) -> str:
        """获取记忆摘要"""
        if not self.conversation_history:
            return "暂无对话历史"

        recent = self.conversation_history[-3:]  # 最近3轮对话
        summary = f"{self.agent_type} Agent最近对话:\n"
        for i, conv in enumerate(recent, 1):
            summary += f"{i}. 用户: {conv['user'][:50]}...\n"
            summary += f"   Agent: {conv['ai'][:50]}...\n"

        return summary
