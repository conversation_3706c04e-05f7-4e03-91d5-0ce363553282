"""
AgentClient: Agent调用和管理客户端
- 专注于Agent的调用、初始化和管理
- Agent自身决定LLM提供者，保持独立性
- 提供统一的Agent交互接口
"""
from typing import Optional, Dict, Any, Union
from loguru import logger


class AgentClient:
    """
    Agent客户端

    专注于Agent的调用和管理，让Agent保持独立性
    """

    def __init__(
        self,
        agent_type: str,
        agent_config: Optional[Dict[str, Any]] = None,
        verbose: bool = False,
        **kwargs
    ):
        """
        初始化Agent客户端

        Args:
            agent_type: Agent类型 (如: "chapter_split")
            agent_config: Agent配置参数
            verbose: 是否输出详细日志
            **kwargs: 其他配置参数
        """
        # 延迟导入避免循环导入
        from .agent_factory import create_agent
        from .base import AgentConfig

        self.agent_type = agent_type
        self.verbose = verbose

        # 构建Agent配置
        config_dict = agent_config or {}
        config_dict.update(kwargs)
        config_dict["verbose"] = verbose

        # 创建Agent配置对象
        self.config = AgentConfig(
            agent_type=agent_type,
            **config_dict
        )

        # 创建Agent实例（Agent自己决定LLM提供者）
        self.agent = create_agent(agent_type, self.config)

        if self.verbose:
            logger.info(f"Agent Client initialized with {agent_type} Agent")

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理任务请求

        Args:
            input_data: 输入数据

        Returns:
            处理结果
        """
        try:
            result = self.agent.process(input_data)

            if self.verbose:
                logger.info(f"Task processed successfully by {self.agent_type} Agent")

            return result

        except Exception as e:
            logger.error(f"Task processing failed: {e}")
            return {"error": str(e)}

    def chat(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        与Agent对话

        Args:
            message: 用户消息
            context: 上下文信息

        Returns:
            Agent回复
        """
        try:
            response = self.agent.chat(message, context)

            if self.verbose:
                logger.info(f"Chat completed with {self.agent_type} Agent")

            return response

        except Exception as e:
            logger.error(f"Chat failed: {e}")
            return f"抱歉，与Agent对话时出现错误: {str(e)}"

    def get_agent_capabilities(self) -> list:
        """获取Agent能力列表"""
        return self.agent.get_capabilities()

    def get_agent_info(self) -> Dict[str, Any]:
        """获取Agent详细信息"""
        agent_info = self.agent.get_agent_info()

        # 添加客户端信息
        agent_info.update({
            "client_type": "AgentClient",
            "agent_type": self.agent_type,
            "verbose": self.verbose
        })

        return agent_info

    def reset_agent_memory(self):
        """重置Agent记忆"""
        if hasattr(self.agent, 'reset_memory'):
            self.agent.reset_memory()
            logger.info(f"{self.agent_type} Agent memory reset")
        else:
            logger.warning(f"{self.agent_type} Agent does not support memory reset")

    def get_memory_summary(self) -> str:
        """获取Agent记忆摘要"""
        if hasattr(self.agent, 'get_memory_summary'):
            return self.agent.get_memory_summary()
        else:
            return f"{self.agent_type} Agent不支持记忆摘要功能"


class MultiAgentClient:
    """
    多Agent客户端

    管理多个Agent实例，支持Agent间协作
    """

    def __init__(self, verbose: bool = False):
        """
        初始化多Agent客户端

        Args:
            verbose: 是否输出详细日志
        """
        # 延迟导入避免循环导入
        from .base import BaseAgent

        self.agents: Dict[str, BaseAgent] = {}
        self.verbose = verbose

        if self.verbose:
            logger.info("Multi-Agent Client initialized")

    def add_agent(
        self,
        agent_name: str,
        agent_type: str,
        agent_config: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        添加Agent

        Args:
            agent_name: Agent实例名称
            agent_type: Agent类型
            agent_config: Agent配置

        Returns:
            是否添加成功
        """
        try:
            # 延迟导入避免循环导入
            from .agent_factory import create_agent
            from .base import AgentConfig

            config_dict = agent_config or {}
            config_dict["verbose"] = self.verbose

            config = AgentConfig(agent_type=agent_type, **config_dict)
            agent = create_agent(agent_type, config)

            self.agents[agent_name] = agent

            if self.verbose:
                logger.info(f"Added {agent_type} Agent as '{agent_name}'")

            return True

        except Exception as e:
            logger.error(f"Failed to add agent '{agent_name}': {e}")
            return False

    def get_agent(self, agent_name: str):
        """获取指定Agent"""
        return self.agents.get(agent_name)

    def list_agents(self) -> Dict[str, str]:
        """列出所有Agent"""
        return {name: agent.agent_type for name, agent in self.agents.items()}

    def process_with_agent(
        self,
        agent_name: str,
        input_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        使用指定Agent处理任务

        Args:
            agent_name: Agent名称
            input_data: 输入数据

        Returns:
            处理结果
        """
        agent = self.get_agent(agent_name)
        if not agent:
            return {"error": f"Agent '{agent_name}' not found"}

        try:
            return agent.process(input_data)
        except Exception as e:
            logger.error(f"Processing failed with agent '{agent_name}': {e}")
            return {"error": str(e)}

    def chat_with_agent(
        self,
        agent_name: str,
        message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        与指定Agent对话

        Args:
            agent_name: Agent名称
            message: 用户消息
            context: 上下文信息

        Returns:
            Agent回复
        """
        agent = self.get_agent(agent_name)
        if not agent:
            return f"Agent '{agent_name}' not found"

        try:
            return agent.chat(message, context)
        except Exception as e:
            logger.error(f"Chat failed with agent '{agent_name}': {e}")
            return f"与Agent '{agent_name}' 对话时出现错误: {str(e)}"


# 便捷函数
def get_available_agent_types() -> list:
    """获取可用的Agent类型列表"""
    from .agent_factory import get_available_agents
    return get_available_agents()


def create_agent_client(
    agent_type: str,
    agent_config: Optional[Dict[str, Any]] = None,
    verbose: bool = False,
    **kwargs
) -> AgentClient:
    """
    创建Agent客户端的便捷函数

    Args:
        agent_type: Agent类型
        agent_config: Agent配置
        verbose: 是否详细日志
        **kwargs: 其他参数

    Returns:
        Agent客户端实例
    """
    return AgentClient(
        agent_type=agent_type,
        agent_config=agent_config,
        verbose=verbose,
        **kwargs
    )


def create_multi_agent_client(verbose: bool = False) -> MultiAgentClient:
    """
    创建多Agent客户端的便捷函数

    Args:
        verbose: 是否详细日志

    Returns:
        多Agent客户端实例
    """
    return MultiAgentClient(verbose=verbose)


# 向后兼容的别名
class EnhancedAgentClient(AgentClient):
    """
    增强Agent客户端（向后兼容别名）

    实际上就是AgentClient，保持向后兼容
    """

    def __init__(
        self,
        agent_type: str = "chapter_split",
        llm_provider: str = "qwen",
        memory_type: str = "conversation",
        verbose: bool = False,
        **kwargs
    ):
        """
        向后兼容的初始化方法

        Args:
            agent_type: Agent类型
            llm_provider: LLM提供者（由Agent自己决定，此参数被忽略）
            memory_type: 记忆类型（由Agent自己决定，此参数被忽略）
            verbose: 是否详细日志
            **kwargs: 其他参数
        """
        # 忽略llm_provider和memory_type，让Agent自己决定
        super().__init__(
            agent_type=agent_type,
            verbose=verbose,
            **kwargs
        )

        if verbose:
            logger.info(f"Enhanced Agent Client (compatibility mode) initialized with {agent_type} Agent")


# 导出主要类和函数
__all__ = [
    "AgentClient",
    "MultiAgentClient",
    "EnhancedAgentClient",
    "create_agent_client",
    "create_multi_agent_client",
    "get_available_agent_types"
]