"""
章节分割Agent - 垂直架构版本
基于新架构的专业章节分割智能体
"""

import json
from typing import Dict, Any, List, Optional
from pathlib import Path
from loguru import logger

from ...base import BaseAgent, AgentConfig
from ...agent_factory import register_agent
from .tools import RegexTool, PatternAnalysisTool
from .prompts import ChapterAnalysisPrompt, ChapterEvaluationPrompt
from .memory import PatternMemory


@register_agent("chapter_split", {
    "description": "专业的章节分割智能体",
    "capabilities": ["智能章节分析", "正则表达式生成", "规则学习优化", "分割质量评估"],
    "version": "2.0.0"
})
class ChapterSplitAgent(BaseAgent):
    """
    章节分割专业Agent - 垂直架构版本
    
    专门负责小说章节分割任务，集成了：
    - 智能正则表达式生成
    - 章节结构分析  
    - 规则学习和优化
    - 分割质量评估
    """
    
    def __init__(self, config: AgentConfig, **kwargs):
        """
        初始化章节分割Agent
        
        Args:
            config: Agent配置
            **kwargs: 额外参数
        """
        super().__init__(config, **kwargs)
        
        # 专业配置
        self.fuzzy_detection_threshold = 30000  # 3万字未找到章节则触发分析
        self.max_analysis_attempts = 3
        self.snippet_size = 3000
        
        # 初始化专用组件
        self._initialize_specialized_components()
        
        # 加载知识库
        self._load_knowledge_base()
        
        logger.info(f"章节分割Agent初始化完成 (垂直架构版本)")
    
    def _initialize_specialized_components(self):
        """初始化专用组件"""
        # 专用工具
        self.regex_tool = RegexTool()
        self.pattern_tool = PatternAnalysisTool()
        
        # 专用提示模板
        self.analysis_prompt = ChapterAnalysisPrompt()
        self.evaluation_prompt = ChapterEvaluationPrompt()
        
        # 专用记忆
        memory_path = self._get_agent_path() / "memory"
        self.pattern_memory = PatternMemory(memory_path)
    
    def _load_knowledge_base(self):
        """加载知识库"""
        knowledge_file = self._get_agent_path() / "knowledge" / "patterns.json"
        try:
            if knowledge_file.exists():
                with open(knowledge_file, 'r', encoding='utf-8') as f:
                    self.knowledge_base = json.load(f)
                logger.debug("加载章节分割知识库成功")
            else:
                self.knowledge_base = {}
                logger.warning("知识库文件不存在")
        except Exception as e:
            logger.error(f"加载知识库失败: {e}")
            self.knowledge_base = {}
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理章节分割请求
        
        Args:
            input_data: 输入数据，包含：
                - content: 小说内容
                - novel_id: 小说ID (可选)
                - force_analysis: 是否强制分析 (可选)
                - method: 分割方法 (可选)
                
        Returns:
            处理结果，包含：
                - chapters: 章节列表
                - quality_score: 质量评分
                - method_used: 使用的方法
                - patterns_found: 发现的模式
        """
        content = input_data.get("content", "")
        novel_id = input_data.get("novel_id")
        force_analysis = input_data.get("force_analysis", False)
        method = input_data.get("method", "auto")
        
        if not content:
            return {"error": "内容不能为空"}
        
        logger.info(f"开始处理章节分割请求，内容长度: {len(content)}字")
        
        try:
            # 执行章节分割
            if method == "auto":
                chapters = self.smart_chapter_split(content, force_analysis)
            elif method == "pattern":
                chapters = self._split_by_patterns(content)
            elif method == "length":
                chapters = self._split_by_length(content)
            else:
                return {"error": f"不支持的分割方法: {method}"}
            
            # 评估分割质量
            quality_result = self.evaluate_split_quality(chapters)
            
            # 记录使用的模式
            patterns_used = self.pattern_memory.get_all_patterns()
            
            return {
                "chapters": chapters,
                "quality_score": quality_result.get("quality_score", 0),
                "quality_details": quality_result,
                "method_used": method,
                "patterns_found": patterns_used,
                "total_chapters": len(chapters),
                "avg_chapter_length": sum(ch.get("word_count", 0) for ch in chapters) // len(chapters) if chapters else 0
            }
            
        except Exception as e:
            logger.error(f"章节分割处理失败: {e}")
            return {"error": str(e)}
    
    def smart_chapter_split(self, content: str, force_analysis: bool = False) -> List[Dict[str, Any]]:
        """
        智能章节分割
        
        Args:
            content: 小说内容
            force_analysis: 是否强制进行Agent分析
            
        Returns:
            章节列表
        """
        logger.info("开始智能章节分割")
        
        # 1. 先尝试现有模式
        if not force_analysis:
            chapters = self._split_by_existing_patterns(content)
            if chapters and len(chapters) > 1:
                logger.info(f"使用现有模式成功分割，章节数: {len(chapters)}")
                return chapters
        
        # 2. Agent智能分析
        logger.info("现有模式无效，启动Agent智能分析...")
        new_patterns = self._agent_analyze_content(content)
        
        # 3. 更新模式记忆
        if new_patterns:
            self.pattern_memory.add_patterns(new_patterns)
        
        # 4. 使用新模式分割
        chapters = self._split_by_existing_patterns(content)
        if chapters and len(chapters) > 1:
            logger.info(f"使用Agent分析的新模式成功分割，章节数: {len(chapters)}")
            return chapters
        
        # 5. 降级到长度分割
        logger.warning("Agent分析后仍无法分割，使用长度分割")
        return self._split_by_length(content)
    
    def _split_by_existing_patterns(self, content: str) -> List[Dict[str, Any]]:
        """使用现有模式分割"""
        all_patterns = self.pattern_memory.get_all_patterns()
        
        # 合并所有模式
        combined_patterns = []
        for pattern_type, patterns in all_patterns.items():
            combined_patterns.extend(patterns)
        
        if not combined_patterns:
            return []
        
        # 使用正则工具分割
        chapters = []
        for pattern in combined_patterns:
            try:
                matches = self.regex_tool._find_all_matches(pattern, content)
                
                if matches and len(matches) > 1:
                    # 找到有效模式，进行分割
                    chapters = self._create_chapters_from_matches(content, matches, pattern)
                    if chapters:
                        # 更新模式统计
                        self.pattern_memory.update_pattern_stats(pattern, success=True)
                        return chapters
                else:
                    # 模式无效
                    self.pattern_memory.update_pattern_stats(pattern, success=False)
                    
            except Exception as e:
                logger.error(f"使用模式分割失败 {pattern}: {e}")
                self.pattern_memory.update_pattern_stats(pattern, success=False)
        
        return []
    
    def _agent_analyze_content(self, content: str) -> Dict[str, List[str]]:
        """Agent分析内容，提取新模式"""
        all_patterns = {"intro": [], "regular": [], "anywhere": []}
        
        # 首先使用模式分析工具
        try:
            # 提取潜在标题
            potential_titles = self.pattern_tool._extract_titles(content[:10000])

            if potential_titles:
                # 分类模式
                title_texts = [t["text"] for t in potential_titles[:20]]  # 取前20个
                classified = self.pattern_tool._classify_patterns(title_texts)

                # 建议正则表达式
                suggestions = self.pattern_tool._suggest_regex(title_texts)
                
                # 解析建议
                for suggestion in suggestions:
                    pattern_type = suggestion.get("type", "regular")
                    regex = suggestion.get("regex", "")
                    if regex and pattern_type in all_patterns:
                        all_patterns[pattern_type].append(regex)
        
        except Exception as e:
            logger.error(f"模式分析工具失败: {e}")
        
        # 然后使用LLM分析
        for attempt in range(self.max_analysis_attempts):
            start_idx = attempt * self.snippet_size
            end_idx = start_idx + self.snippet_size
            snippet = content[start_idx:end_idx]
            
            if not snippet:
                break
            
            try:
                # 构建上下文
                context = f"已发现模式数量: intro={len(all_patterns['intro'])}, regular={len(all_patterns['regular'])}, anywhere={len(all_patterns['anywhere'])}"
                
                # 使用分析提示模板
                prompt = self.analysis_prompt.format(
                    text_snippet=snippet,
                    intro_patterns=all_patterns["intro"],
                    regular_patterns=all_patterns["regular"],
                    anywhere_patterns=all_patterns["anywhere"],
                    context=context
                )
                
                # LLM分析
                response = self.llm_client.chat(prompt)
                
                # 解析响应
                patterns = self._parse_analysis_response(response)
                
                # 合并结果
                for pattern_type, pattern_list in patterns.items():
                    if pattern_type in all_patterns:
                        all_patterns[pattern_type].extend(pattern_list)
                        
            except Exception as e:
                logger.error(f"LLM分析失败 (尝试 {attempt+1}): {e}")
        
        # 去重
        for pattern_type in all_patterns:
            all_patterns[pattern_type] = list(set(all_patterns[pattern_type]))
        
        logger.info(f"Agent分析完成，发现模式: {sum(len(p) for p in all_patterns.values())}个")
        return all_patterns

    def _parse_analysis_response(self, response: str) -> Dict[str, List[str]]:
        """解析Agent的分析响应"""
        patterns = {"intro": [], "regular": [], "anywhere": []}

        try:
            lines = response.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('intro_patterns:'):
                    content = line.replace('intro_patterns:', '').strip()
                    if content:
                        patterns["intro"] = [p.strip() for p in content.split(',') if p.strip()]
                elif line.startswith('regular_patterns:'):
                    content = line.replace('regular_patterns:', '').strip()
                    if content:
                        patterns["regular"] = [p.strip() for p in content.split(',') if p.strip()]
                elif line.startswith('anywhere_patterns:'):
                    content = line.replace('anywhere_patterns:', '').strip()
                    if content:
                        patterns["anywhere"] = [p.strip() for p in content.split(',') if p.strip()]

        except Exception as e:
            logger.error(f"解析Agent响应失败: {e}")

        return patterns

    def _create_chapters_from_matches(self, content: str, matches: List[Dict[str, Any]], pattern: str) -> List[Dict[str, Any]]:
        """根据匹配结果创建章节"""
        if not matches:
            return []

        chapters = []

        # 按位置排序
        matches.sort(key=lambda x: x["span"][0])

        for i, match in enumerate(matches):
            start_pos = match["span"][0]

            # 确定章节结束位置
            if i < len(matches) - 1:
                end_pos = matches[i + 1]["span"][0]
            else:
                end_pos = len(content)

            # 提取章节内容
            chapter_content = content[start_pos:end_pos].strip()

            # 提取标题
            title = match["match_text"].strip()

            chapters.append({
                "chapter_index": i + 1,
                "title": title,
                "content": chapter_content,
                "word_count": len(chapter_content),
                "start_pos": start_pos,
                "end_pos": end_pos,
                "pattern_used": pattern
            })

        return chapters

    def _split_by_patterns(self, content: str) -> List[Dict[str, Any]]:
        """使用模式分割（公开方法）"""
        return self._split_by_existing_patterns(content)

    def _split_by_length(self, content: str, chunk_size: int = 10000) -> List[Dict[str, Any]]:
        """按长度分割（降级方案）"""
        chapters = []
        total_length = len(content)
        chapter_index = 1

        for i in range(0, total_length, chunk_size):
            chunk = content[i:i+chunk_size]
            chapters.append({
                "chapter_index": chapter_index,
                "title": f"第{chapter_index}章",
                "content": chunk,
                "word_count": len(chunk),
                "start_pos": i,
                "end_pos": min(i + chunk_size, total_length),
                "pattern_used": "length_split"
            })
            chapter_index += 1

        return chapters

    def evaluate_split_quality(self, chapters: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        评估分割质量

        Args:
            chapters: 章节列表

        Returns:
            质量评估结果
        """
        if not chapters:
            return {"quality_score": 0, "issues": ["无章节"], "grade": "失败"}

        issues = []
        scores = {}

        # 1. 章节数量评估
        chapter_count = len(chapters)
        if chapter_count < 2:
            issues.append("章节数量过少")
            scores["count_score"] = 0
        elif chapter_count > 500:
            issues.append("章节数量过多")
            scores["count_score"] = 60
        else:
            scores["count_score"] = 100

        # 2. 章节长度评估
        lengths = [ch.get("word_count", 0) for ch in chapters]
        avg_length = sum(lengths) / len(lengths)

        if avg_length < 500:
            issues.append("平均章节长度过短")
            scores["length_score"] = 40
        elif avg_length > 20000:
            issues.append("平均章节长度过长")
            scores["length_score"] = 60
        else:
            scores["length_score"] = 100

        # 3. 长度均匀性评估
        if len(lengths) > 1:
            length_variance = sum((l - avg_length) ** 2 for l in lengths) / len(lengths)
            length_std = length_variance ** 0.5
            cv = length_std / avg_length if avg_length > 0 else 1

            if cv > 0.8:
                issues.append("章节长度差异过大")
                scores["uniformity_score"] = 50
            elif cv > 0.5:
                scores["uniformity_score"] = 75
            else:
                scores["uniformity_score"] = 100
        else:
            scores["uniformity_score"] = 100

        # 4. 标题规范性评估
        titles = [ch.get("title", "") for ch in chapters]
        title_patterns = set()
        for title in titles:
            if "第" in title and "章" in title:
                title_patterns.add("standard_chapter")
            elif "第" in title and "回" in title:
                title_patterns.add("standard_hui")
            else:
                title_patterns.add("other")

        if len(title_patterns) == 1 and "other" not in title_patterns:
            scores["title_score"] = 100
        elif "other" in title_patterns and len(title_patterns) > 1:
            scores["title_score"] = 60
            issues.append("标题格式不统一")
        else:
            scores["title_score"] = 80

        # 计算总分
        total_score = sum(scores.values()) / len(scores)

        # 确定等级
        if total_score >= 90:
            grade = "优秀"
        elif total_score >= 75:
            grade = "良好"
        elif total_score >= 60:
            grade = "一般"
        else:
            grade = "较差"

        return {
            "quality_score": round(total_score, 1),
            "grade": grade,
            "chapter_count": chapter_count,
            "avg_length": round(avg_length, 1),
            "length_std": round(length_std, 1) if len(lengths) > 1 else 0,
            "issues": issues,
            "detailed_scores": scores,
            "recommendations": self._generate_recommendations(issues, scores)
        }

    def _generate_recommendations(self, issues: List[str], scores: Dict[str, float]) -> List[str]:
        """生成改进建议"""
        recommendations = []

        if "章节数量过少" in issues:
            recommendations.append("建议检查分割规则，可能存在未识别的章节")
        if "章节数量过多" in issues:
            recommendations.append("建议优化分割规则，避免过度分割")
        if "平均章节长度过短" in issues:
            recommendations.append("建议合并相邻的短章节")
        if "平均章节长度过长" in issues:
            recommendations.append("建议进一步细分长章节")
        if "章节长度差异过大" in issues:
            recommendations.append("建议平衡章节长度，调整分割点")
        if "标题格式不统一" in issues:
            recommendations.append("建议统一章节标题格式")

        if not recommendations:
            recommendations.append("分割质量良好，无需特别调整")

        return recommendations

    def get_capabilities(self) -> List[str]:
        """获取Agent能力列表"""
        return [
            "智能章节模式分析",
            "正则表达式自动生成",
            "规则学习和优化",
            "分割质量评估",
            "多种分割方法支持",
            "模式记忆管理",
            "专业工具集成"
        ]
