"""
正则表达式工具
专门用于章节分割的正则表达式处理
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger

try:
    from ....base import BaseTool
except ImportError:
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent.parent))
    from base import BaseTool


class RegexTool(BaseTool):
    """
    正则表达式工具
    
    提供章节分割相关的正则表达式功能
    """
    
    def __init__(self):
        super().__init__(
            name="regex_tool",
            description="正则表达式处理工具，用于章节分割"
        )
    
    def execute(self, **kwargs) -> Any:
        """
        执行正则表达式操作
        
        支持的操作：
        - match: 匹配文本
        - find_all: 查找所有匹配
        - split: 分割文本
        - validate: 验证正则表达式
        """
        operation = kwargs.get("operation")
        
        if operation == "match":
            return self._match_text(**kwargs)
        elif operation == "find_all":
            return self._find_all_matches(**kwargs)
        elif operation == "split":
            return self._split_text(**kwargs)
        elif operation == "validate":
            return self._validate_regex(**kwargs)
        else:
            raise ValueError(f"不支持的操作: {operation}")
    
    def _match_text(self, pattern: str, text: str, flags: int = 0) -> Optional[Dict[str, Any]]:
        """匹配文本"""
        try:
            match = re.match(pattern, text, flags)
            if match:
                return {
                    "matched": True,
                    "groups": match.groups(),
                    "groupdict": match.groupdict(),
                    "span": match.span(),
                    "match_text": match.group(0)
                }
            return {"matched": False}
        except Exception as e:
            logger.error(f"正则匹配失败: {e}")
            return {"matched": False, "error": str(e)}
    
    def _find_all_matches(self, pattern: str, text: str, flags: int = 0) -> List[Dict[str, Any]]:
        """查找所有匹配"""
        try:
            matches = []
            for match in re.finditer(pattern, text, flags):
                matches.append({
                    "groups": match.groups(),
                    "groupdict": match.groupdict(),
                    "span": match.span(),
                    "match_text": match.group(0)
                })
            return matches
        except Exception as e:
            logger.error(f"正则查找失败: {e}")
            return []
    
    def _split_text(self, pattern: str, text: str, flags: int = 0) -> List[str]:
        """分割文本"""
        try:
            return re.split(pattern, text, flags=flags)
        except Exception as e:
            logger.error(f"正则分割失败: {e}")
            return [text]
    
    def _validate_regex(self, pattern: str) -> Dict[str, Any]:
        """验证正则表达式"""
        try:
            re.compile(pattern)
            return {"valid": True}
        except re.error as e:
            return {"valid": False, "error": str(e)}
    
    def validate_params(self, **kwargs) -> bool:
        """验证参数"""
        operation = kwargs.get("operation")
        if not operation:
            return False
        
        if operation in ["match", "find_all", "split"]:
            return "pattern" in kwargs and "text" in kwargs
        elif operation == "validate":
            return "pattern" in kwargs
        
        return False
    
    def find_chapter_patterns(self, text: str, max_patterns: int = 10) -> List[Dict[str, Any]]:
        """
        查找可能的章节模式
        
        Args:
            text: 要分析的文本
            max_patterns: 最大模式数量
            
        Returns:
            可能的章节模式列表
        """
        patterns = []
        
        # 常见的章节模式
        common_patterns = [
            r"第[零一二三四五六七八九十百千万亿\d]+章.*?",
            r"第[零一二三四五六七八九十百千万亿\d]+回.*?",
            r"Chapter\s*\d+.*?",
            r"第\d+章.*?",
            r"第\d+回.*?",
            r"序章.*?",
            r"前言.*?",
            r"引言.*?",
            r"尾声.*?",
            r"后记.*?",
            r"终章.*?"
        ]
        
        for pattern in common_patterns:
            matches = self._find_all_matches(pattern, text, re.IGNORECASE)
            if matches:
                patterns.append({
                    "pattern": pattern,
                    "matches": len(matches),
                    "examples": [m["match_text"] for m in matches[:3]]
                })
        
        # 按匹配数量排序
        patterns.sort(key=lambda x: x["matches"], reverse=True)
        return patterns[:max_patterns]
