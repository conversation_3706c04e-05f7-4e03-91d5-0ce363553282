"""
模式分析工具
专门用于分析文本中的章节模式
"""

import re
from typing import List, Dict, Any, Optional, Set
from collections import Counter
from loguru import logger

try:
    from ....base import BaseTool
except ImportError:
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent.parent))
    from base import BaseTool


class PatternAnalysisTool(BaseTool):
    """
    模式分析工具
    
    分析文本中的章节模式和结构
    """
    
    def __init__(self):
        super().__init__(
            name="pattern_analysis_tool",
            description="分析文本中的章节模式和结构"
        )
    
    def execute(self, **kwargs) -> Any:
        """
        执行模式分析
        
        支持的操作：
        - analyze_structure: 分析文本结构
        - extract_titles: 提取可能的标题
        - classify_patterns: 分类模式
        - suggest_regex: 建议正则表达式
        """
        operation = kwargs.get("operation")
        
        if operation == "analyze_structure":
            return self._analyze_structure(**kwargs)
        elif operation == "extract_titles":
            return self._extract_titles(**kwargs)
        elif operation == "classify_patterns":
            return self._classify_patterns(**kwargs)
        elif operation == "suggest_regex":
            return self._suggest_regex(**kwargs)
        else:
            raise ValueError(f"不支持的操作: {operation}")
    
    def _analyze_structure(self, text: str, sample_size: int = 5000) -> Dict[str, Any]:
        """分析文本结构"""
        # 取样分析
        sample_text = text[:sample_size] if len(text) > sample_size else text
        
        lines = sample_text.split('\n')
        
        # 分析行特征
        line_features = []
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            features = {
                "line_number": i,
                "text": line,
                "length": len(line),
                "has_numbers": bool(re.search(r'\d+', line)),
                "has_chapter_keywords": bool(re.search(r'[章回节部篇卷]', line)),
                "is_short": len(line) < 50,
                "starts_with_number": bool(re.match(r'^\d+', line)),
                "starts_with_chinese_number": bool(re.match(r'^[零一二三四五六七八九十百千万亿]', line))
            }
            line_features.append(features)
        
        return {
            "total_lines": len(lines),
            "analyzed_lines": len(line_features),
            "line_features": line_features[:20],  # 返回前20行的特征
            "structure_summary": self._summarize_structure(line_features)
        }
    
    def _extract_titles(self, text: str, confidence_threshold: float = 0.6) -> List[Dict[str, Any]]:
        """提取可能的标题"""
        lines = text.split('\n')
        potential_titles = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # 计算标题置信度
            confidence = self._calculate_title_confidence(line)
            
            if confidence >= confidence_threshold:
                potential_titles.append({
                    "line_number": i,
                    "text": line,
                    "confidence": confidence,
                    "features": self._analyze_title_features(line)
                })
        
        # 按置信度排序
        potential_titles.sort(key=lambda x: x["confidence"], reverse=True)
        return potential_titles
    
    def _classify_patterns(self, titles: List[str]) -> Dict[str, List[str]]:
        """分类模式"""
        patterns = {
            "intro": [],      # 序章、前言等
            "regular": [],    # 正文章节
            "anywhere": []    # 尾声、后记等
        }
        
        intro_keywords = ["序章", "前言", "引言", "开篇", "楔子", "序"]
        anywhere_keywords = ["尾声", "后记", "终章", "结语", "跋", "附录"]
        
        for title in titles:
            title_lower = title.lower()
            
            # 检查是否为intro类型
            if any(keyword in title for keyword in intro_keywords):
                patterns["intro"].append(title)
            # 检查是否为anywhere类型
            elif any(keyword in title for keyword in anywhere_keywords):
                patterns["anywhere"].append(title)
            # 其他归为regular类型
            else:
                patterns["regular"].append(title)
        
        return patterns
    
    def _suggest_regex(self, titles: List[str]) -> List[Dict[str, Any]]:
        """建议正则表达式"""
        suggestions = []
        
        # 分析数字模式
        number_patterns = self._analyze_number_patterns(titles)
        for pattern_info in number_patterns:
            suggestions.append({
                "type": "number_pattern",
                "regex": pattern_info["regex"],
                "description": pattern_info["description"],
                "examples": pattern_info["examples"]
            })
        
        # 分析关键词模式
        keyword_patterns = self._analyze_keyword_patterns(titles)
        for pattern_info in keyword_patterns:
            suggestions.append({
                "type": "keyword_pattern",
                "regex": pattern_info["regex"],
                "description": pattern_info["description"],
                "examples": pattern_info["examples"]
            })
        
        return suggestions
    
    def _calculate_title_confidence(self, line: str) -> float:
        """计算标题置信度"""
        confidence = 0.0
        
        # 长度因子（标题通常较短）
        if len(line) < 50:
            confidence += 0.2
        elif len(line) < 20:
            confidence += 0.3
        
        # 包含章节关键词
        if re.search(r'[章回节部篇卷]', line):
            confidence += 0.4
        
        # 包含数字
        if re.search(r'\d+', line):
            confidence += 0.2
        
        # 包含中文数字
        if re.search(r'[零一二三四五六七八九十百千万亿]', line):
            confidence += 0.2
        
        # 特殊关键词
        special_keywords = ["序章", "前言", "引言", "尾声", "后记", "终章"]
        if any(keyword in line for keyword in special_keywords):
            confidence += 0.3
        
        return min(confidence, 1.0)
    
    def _analyze_title_features(self, title: str) -> Dict[str, Any]:
        """分析标题特征"""
        return {
            "length": len(title),
            "has_numbers": bool(re.search(r'\d+', title)),
            "has_chinese_numbers": bool(re.search(r'[零一二三四五六七八九十百千万亿]', title)),
            "has_chapter_keywords": bool(re.search(r'[章回节部篇卷]', title)),
            "starts_with_number": bool(re.match(r'^\d+', title)),
            "contains_colon": ':' in title or '：' in title
        }
    
    def _summarize_structure(self, line_features: List[Dict[str, Any]]) -> Dict[str, Any]:
        """总结结构特征"""
        if not line_features:
            return {}
        
        total_lines = len(line_features)
        short_lines = sum(1 for f in line_features if f["is_short"])
        lines_with_numbers = sum(1 for f in line_features if f["has_numbers"])
        lines_with_keywords = sum(1 for f in line_features if f["has_chapter_keywords"])
        
        return {
            "short_line_ratio": short_lines / total_lines,
            "number_line_ratio": lines_with_numbers / total_lines,
            "keyword_line_ratio": lines_with_keywords / total_lines,
            "avg_line_length": sum(f["length"] for f in line_features) / total_lines
        }
    
    def _analyze_number_patterns(self, titles: List[str]) -> List[Dict[str, Any]]:
        """分析数字模式"""
        patterns = []
        
        # 阿拉伯数字模式
        arabic_titles = [t for t in titles if re.search(r'\d+', t)]
        if arabic_titles:
            patterns.append({
                "regex": r"第\d+章.*?",
                "description": "阿拉伯数字章节",
                "examples": arabic_titles[:3]
            })
        
        # 中文数字模式
        chinese_titles = [t for t in titles if re.search(r'[零一二三四五六七八九十百千万亿]', t)]
        if chinese_titles:
            patterns.append({
                "regex": r"第[零一二三四五六七八九十百千万亿]+章.*?",
                "description": "中文数字章节",
                "examples": chinese_titles[:3]
            })
        
        return patterns
    
    def _analyze_keyword_patterns(self, titles: List[str]) -> List[Dict[str, Any]]:
        """分析关键词模式"""
        patterns = []
        
        # 统计关键词频率
        keywords = []
        for title in titles:
            keywords.extend(re.findall(r'[章回节部篇卷]', title))
        
        keyword_counts = Counter(keywords)
        
        for keyword, count in keyword_counts.most_common(3):
            if count > 1:
                patterns.append({
                    "regex": f".*?{keyword}.*?",
                    "description": f"包含'{keyword}'的标题",
                    "examples": [t for t in titles if keyword in t][:3]
                })
        
        return patterns
    
    def validate_params(self, **kwargs) -> bool:
        """验证参数"""
        operation = kwargs.get("operation")
        if not operation:
            return False
        
        if operation in ["analyze_structure", "extract_titles"]:
            return "text" in kwargs
        elif operation == "classify_patterns":
            return "titles" in kwargs
        elif operation == "suggest_regex":
            return "titles" in kwargs
        
        return False
