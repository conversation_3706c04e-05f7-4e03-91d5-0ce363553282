"""
章节评估提示模板
"""

try:
    from ....base import BasePrompt
except ImportError:
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent.parent))
    from base import BasePrompt


class ChapterEvaluationPrompt(BasePrompt):
    """
    章节评估提示模板
    
    用于评估章节分割的质量
    """
    
    def __init__(self):
        super().__init__(
            name="chapter_evaluation",
            description="评估章节分割的质量和合理性"
        )
    
    def format(self, **kwargs) -> str:
        """格式化提示模板"""
        chapters = kwargs.get("chapters", [])
        original_length = kwargs.get("original_length", 0)
        split_method = kwargs.get("split_method", "unknown")
        
        # 构建章节信息摘要
        chapter_summary = []
        for i, chapter in enumerate(chapters[:10]):  # 只显示前10章
            title = chapter.get("title", f"第{i+1}章")
            word_count = chapter.get("word_count", 0)
            chapter_summary.append(f"- {title}: {word_count}字")
        
        if len(chapters) > 10:
            chapter_summary.append(f"... 还有{len(chapters)-10}章")
        
        template = f"""你是小说编辑专家，请评估以下章节分割的质量。

## 分割信息
- 原文总长度: {original_length}字
- 分割方法: {split_method}
- 章节总数: {len(chapters)}章
- 平均章节长度: {original_length // len(chapters) if chapters else 0}字

## 章节列表
{chr(10).join(chapter_summary)}

## 评估维度
请从以下维度评估分割质量（1-10分）：

1. **章节数量合理性** (1-10分)
   - 章节数量是否符合小说长度
   - 是否过多或过少

2. **章节长度均匀性** (1-10分)
   - 各章节长度是否相对均匀
   - 是否存在过长或过短的章节

3. **标题规范性** (1-10分)
   - 章节标题是否规范统一
   - 是否符合常见的命名规范

4. **内容完整性** (1-10分)
   - 章节分割是否破坏了内容的完整性
   - 是否在合适的位置进行分割

5. **整体质量** (1-10分)
   - 综合评估分割效果
   - 是否达到可用标准

## 输出格式
请按以下格式输出评估结果：

### 评分结果
- 章节数量合理性: X/10分
- 章节长度均匀性: X/10分  
- 标题规范性: X/10分
- 内容完整性: X/10分
- 整体质量: X/10分

### 总分
X/50分 (等级: 优秀/良好/一般/较差)

### 问题分析
1. 主要问题1
2. 主要问题2
3. 主要问题3

### 改进建议
1. 改进建议1
2. 改进建议2
3. 改进建议3

### 结论
简要总结分割质量和是否建议使用此分割结果。
"""
        return template
    
    def validate_params(self, **kwargs) -> bool:
        """验证参数"""
        return "chapters" in kwargs


class ChapterQualityAnalysisPrompt(BasePrompt):
    """
    章节质量分析提示模板
    
    深度分析章节分割的质量问题
    """
    
    def __init__(self):
        super().__init__(
            name="chapter_quality_analysis",
            description="深度分析章节分割的质量问题"
        )
    
    def format(self, **kwargs) -> str:
        """格式化提示模板"""
        chapter_stats = kwargs.get("chapter_stats", {})
        sample_chapters = kwargs.get("sample_chapters", [])
        
        template = f"""你是数据分析专家，请深度分析章节分割的统计数据，识别潜在问题。

## 统计数据
{chapter_stats}

## 样本章节
{sample_chapters}

## 分析任务
1. **长度分布分析**
   - 分析章节长度的分布特征
   - 识别异常长度的章节
   - 评估长度分布的合理性

2. **标题模式分析**
   - 分析标题的命名模式
   - 识别不一致的命名规则
   - 评估标题的规范性

3. **内容连贯性分析**
   - 基于样本章节分析内容连贯性
   - 识别可能的分割错误
   - 评估分割位置的合理性

4. **质量问题识别**
   - 列出发现的主要质量问题
   - 按严重程度排序
   - 提供具体的问题位置

## 输出格式
### 长度分布分析
- 分布特征: 
- 异常章节: 
- 合理性评估: 

### 标题模式分析
- 命名模式: 
- 不一致问题: 
- 规范性评估: 

### 内容连贯性分析
- 连贯性评估: 
- 分割错误: 
- 位置合理性: 

### 质量问题清单
1. 严重问题: 
2. 一般问题: 
3. 轻微问题: 

### 改进优先级
1. 高优先级: 
2. 中优先级: 
3. 低优先级: 
"""
        return template
    
    def validate_params(self, **kwargs) -> bool:
        """验证参数"""
        return "chapter_stats" in kwargs
