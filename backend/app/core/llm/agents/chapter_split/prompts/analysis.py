"""
章节分析提示模板
"""

try:
    from ....base import BasePrompt
except ImportError:
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent.parent))
    from base import BasePrompt


class ChapterAnalysisPrompt(BasePrompt):
    """
    章节分析提示模板
    
    用于分析文本中的章节模式
    """
    
    def __init__(self):
        super().__init__(
            name="chapter_analysis",
            description="分析文本中的章节分割模式"
        )
    
    def format(self, **kwargs) -> str:
        """格式化提示模板"""
        text_snippet = kwargs.get("text_snippet", "")
        intro_patterns = kwargs.get("intro_patterns", [])
        regular_patterns = kwargs.get("regular_patterns", [])
        anywhere_patterns = kwargs.get("anywhere_patterns", [])
        context = kwargs.get("context", "")
        
        template = f"""你是一位顶级的正则表达式和小说结构分析专家。请分析以下小说文本片段，提取章节分割规则。

当前已发现的规则：
- intro_patterns: {intro_patterns}
- regular_patterns: {regular_patterns}  
- anywhere_patterns: {anywhere_patterns}

{context}

## 分析要求
1. 只识别**明显的章节标题**，不要把普通段落当作章节
2. intro类型：前言、序章、引言等开头章节
3. regular类型：第X章、第X回、Chapter X等正文章节
4. anywhere类型：尾声、后记、终章等结尾章节
5. 发现编号规律时，请归纳通用正则表达式
6. 避免与已有规则重复或冲突

## 待分析文本
{text_snippet}

## 输出格式
请严格按照以下格式输出：
intro_patterns: 规则1, 规则2, ...
regular_patterns: 规则1, 规则2, ...
anywhere_patterns: 规则1, 规则2, ...

如果某类型没有发现新规则，请留空。

## 分析示例
如果发现"第一章 开始"、"第二章 冒险"等模式，应输出：
regular_patterns: 第[零一二三四五六七八九十百千万亿\\d]+章.*?

如果发现"序章：起源"等模式，应输出：
intro_patterns: 序章：.*?
"""
        return template
    
    def validate_params(self, **kwargs) -> bool:
        """验证参数"""
        return "text_snippet" in kwargs


class ChapterPatternSuggestionPrompt(BasePrompt):
    """
    章节模式建议提示模板
    
    基于分析结果建议正则表达式
    """
    
    def __init__(self):
        super().__init__(
            name="chapter_pattern_suggestion",
            description="基于章节分析结果建议正则表达式模式"
        )
    
    def format(self, **kwargs) -> str:
        """格式化提示模板"""
        potential_titles = kwargs.get("potential_titles", [])
        structure_info = kwargs.get("structure_info", {})
        
        template = f"""你是正则表达式专家，请基于以下章节标题分析结果，建议合适的正则表达式模式。

## 潜在章节标题
{potential_titles}

## 文本结构信息
{structure_info}

## 任务要求
1. 分析标题的共同特征和模式
2. 为每种模式生成精确的正则表达式
3. 确保正则表达式能够准确匹配，避免误匹配
4. 按照intro、regular、anywhere三类分类

## 输出格式
请按以下格式输出：

### intro_patterns
- 模式1: 正则表达式 (描述)
- 模式2: 正则表达式 (描述)

### regular_patterns  
- 模式1: 正则表达式 (描述)
- 模式2: 正则表达式 (描述)

### anywhere_patterns
- 模式1: 正则表达式 (描述)
- 模式2: 正则表达式 (描述)

## 注意事项
- 使用.*?进行非贪婪匹配
- 考虑中文和阿拉伯数字的组合
- 避免过于宽泛的匹配规则
- 确保规则的实用性和准确性
"""
        return template
    
    def validate_params(self, **kwargs) -> bool:
        """验证参数"""
        return "potential_titles" in kwargs
