"""
垂直架构使用示例
展示如何使用"万物皆Agent"的垂直架构
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_file = Path(__file__).resolve()
project_root = current_file.parents[5]  # 向上5级到项目根目录
sys.path.insert(0, str(project_root))

from backend.app.core.llm import (
    create_agent, 
    get_available_agents, 
    get_agent_info,
    AgentConfig
)


def example_1_basic_agent_usage():
    """示例1：基础Agent使用"""
    print("=== 示例1：基础Agent使用 ===")
    
    # 查看可用的Agent类型
    available_agents = get_available_agents()
    print(f"可用Agent类型: {available_agents}")
    
    # 获取Agent信息
    for agent_type in available_agents:
        info = get_agent_info(agent_type)
        print(f"\n{agent_type} Agent信息:")
        print(f"  描述: {info.get('description', 'N/A')}")
        print(f"  能力: {info.get('capabilities', [])}")
        print(f"  版本: {info.get('version', 'N/A')}")


def example_2_chapter_split_agent():
    """示例2：章节分割Agent使用"""
    print("\n=== 示例2：章节分割Agent使用 ===")
    
    # 创建Agent配置
    config = AgentConfig(
        agent_type="chapter_split",
        llm_provider="qwen",
        memory_type="conversation",
        verbose=True
    )
    
    # 创建Agent
    agent = create_agent("chapter_split", config)
    print(f"成功创建Agent: {type(agent).__name__}")
    
    # 准备测试内容
    test_content = """
    序章 远古传说
    在遥远的古代，世界被神秘的力量所笼罩...
    这是一个关于勇气和智慧的传说。
    
    第一章 英雄的诞生
    在一个宁静的小村庄里，住着一个名叫艾伦的少年...
    他从小就展现出了不凡的天赋和勇气。
    村民们都说他注定要成为一个伟大的英雄。
    
    第二章 踏上征程
    艾伦十八岁那年，村庄遭到了黑暗势力的袭击...
    为了保护家园，他决定踏上寻找传说中圣剑的征程。
    这是一条充满危险和未知的道路。
    
    第三章 初次试炼
    在前往圣剑所在地的路上，艾伦遇到了第一个试炼...
    他必须证明自己有资格获得这把传说中的武器。
    
    尾声 新的开始
    经过重重考验，艾伦终于获得了圣剑...
    但这只是他传奇人生的开始。
    """
    
    # 处理章节分割
    input_data = {
        "content": test_content,
        "method": "auto",
        "force_analysis": False
    }
    
    result = agent.process(input_data)
    
    if "error" in result:
        print(f"分割失败: {result['error']}")
        return
    
    # 显示结果
    print(f"\n分割结果:")
    print(f"  总章节数: {result['total_chapters']}")
    print(f"  质量评分: {result['quality_score']}")
    print(f"  使用方法: {result['method_used']}")
    print(f"  平均章节长度: {result['avg_chapter_length']}字")
    
    print(f"\n章节列表:")
    for i, chapter in enumerate(result['chapters'], 1):
        title = chapter.get('title', f'第{i}章')
        word_count = chapter.get('word_count', 0)
        print(f"  {i}. {title}: {word_count}字")
    
    # 显示质量详情
    quality_details = result.get('quality_details', {})
    if quality_details:
        print(f"\n质量评估详情:")
        print(f"  等级: {quality_details.get('grade', 'N/A')}")
        print(f"  问题: {quality_details.get('issues', [])}")
        print(f"  建议: {quality_details.get('recommendations', [])}")


def example_3_agent_conversation():
    """示例3：Agent对话功能"""
    print("\n=== 示例3：Agent对话功能 ===")
    
    # 创建Agent
    config = AgentConfig(agent_type="chapter_split", verbose=False)
    agent = create_agent("chapter_split", config)
    
    # 对话示例
    questions = [
        "你好，请介绍一下你的功能",
        "如何提高章节分割的准确性？",
        "什么样的文本最适合自动章节分割？",
        "你能处理哪些语言的文本？"
    ]
    
    for question in questions:
        print(f"\n用户: {question}")
        response = agent.chat(question)
        print(f"Agent: {response[:200]}...")  # 只显示前200字符


def example_4_agent_memory():
    """示例4：Agent记忆功能"""
    print("\n=== 示例4：Agent记忆功能 ===")
    
    # 创建Agent
    config = AgentConfig(agent_type="chapter_split", verbose=False)
    agent = create_agent("chapter_split", config)
    
    # 测试模式记忆
    test_patterns = {
        "intro": ["序章.*?", "前言.*?", "引言.*?"],
        "regular": ["第\\d+章.*?", "第[一二三四五六七八九十]+章.*?"],
        "anywhere": ["尾声.*?", "后记.*?", "终章.*?"]
    }
    
    # 添加模式到记忆
    success = agent.pattern_memory.add_patterns(test_patterns)
    print(f"模式添加结果: {success}")
    
    # 获取存储的模式
    stored_patterns = agent.pattern_memory.get_all_patterns()
    print(f"存储的模式:")
    for pattern_type, patterns in stored_patterns.items():
        print(f"  {pattern_type}: {len(patterns)}个模式")
        for pattern in patterns[:3]:  # 只显示前3个
            print(f"    - {pattern}")
        if len(patterns) > 3:
            print(f"    ... 还有{len(patterns)-3}个")
    
    # 获取模式有效性统计
    effectiveness = agent.pattern_memory.get_pattern_effectiveness()
    print(f"\n模式有效性统计: {len(effectiveness)}个模式有统计数据")
    
    # 获取最佳模式
    top_patterns = agent.pattern_memory.get_top_patterns(limit=5)
    if top_patterns:
        print(f"\n最佳模式 (前5个):")
        for i, pattern_info in enumerate(top_patterns, 1):
            print(f"  {i}. {pattern_info['pattern']}")
            print(f"     类型: {pattern_info['type']}")
            print(f"     有效性: {pattern_info['effectiveness']:.2f}")
            print(f"     使用次数: {pattern_info['usage_count']}")


def example_5_agent_info():
    """示例5：Agent信息获取"""
    print("\n=== 示例5：Agent信息获取 ===")
    
    # 创建Agent
    config = AgentConfig(agent_type="chapter_split", verbose=False)
    agent = create_agent("chapter_split", config)
    
    # 获取详细信息
    info = agent.get_agent_info()
    print(f"Agent详细信息:")
    for key, value in info.items():
        if isinstance(value, list):
            print(f"  {key}: {len(value)}项")
            for item in value[:3]:  # 只显示前3项
                print(f"    - {item}")
            if len(value) > 3:
                print(f"    ... 还有{len(value)-3}项")
        elif isinstance(value, dict):
            print(f"  {key}: {len(value)}个属性")
            for k, v in list(value.items())[:3]:  # 只显示前3个属性
                print(f"    {k}: {v}")
        else:
            print(f"  {key}: {value}")
    
    # 获取能力列表
    capabilities = agent.get_capabilities()
    print(f"\nAgent能力:")
    for i, capability in enumerate(capabilities, 1):
        print(f"  {i}. {capability}")


def run_all_examples():
    """运行所有示例"""
    print("垂直架构使用示例")
    print("=" * 50)
    
    examples = [
        ("基础Agent使用", example_1_basic_agent_usage),
        ("章节分割Agent", example_2_chapter_split_agent),
        ("Agent对话功能", example_3_agent_conversation),
        ("Agent记忆功能", example_4_agent_memory),
        ("Agent信息获取", example_5_agent_info)
    ]
    
    for name, func in examples:
        try:
            func()
            print(f"\n✓ {name} 示例完成")
        except Exception as e:
            print(f"\n✗ {name} 示例失败: {e}")
        
        print("-" * 50)
    
    print("\n所有示例运行完成！")


if __name__ == "__main__":
    run_all_examples()
