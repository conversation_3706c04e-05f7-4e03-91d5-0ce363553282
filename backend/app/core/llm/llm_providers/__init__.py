"""
LLM提供者模块
统一管理各种LLM提供者的工厂函数和类
"""

from .qwen_llm import get_qwen_llm, EnhancedQwenLLM
# 暂时注释掉有 aiohttp 兼容性问题的导入
# from .ollama_llm import get_ollama_llm, EnhancedOllamaLLM
from .openai_llm import get_openai_llm
# from .cephalon_qwen_llm import get_cephalon_qwen_llm, CephalonQwenLLM

# 提供者映射，便于动态选择
LLM_PROVIDERS = {
    "qwen": get_qwen_llm,
    # "ollama": get_ollama_llm,  # 暂时禁用
    "openai": get_openai_llm,
    # "cephalon_qwen": get_cephalon_qwen_llm,  # 暂时禁用
}

def get_llm_provider(provider_name: str, **kwargs):
    """
    根据提供者名称获取LLM实例

    Args:
        provider_name: 提供者名称 (qwen, ollama, openai, cephalon_qwen)
        **kwargs: 传递给具体提供者的参数

    Returns:
        LLM实例

    Raises:
        ValueError: 当提供者名称不支持时
    """
    if provider_name not in LLM_PROVIDERS:
        raise ValueError(f"Unsupported LLM provider: {provider_name}. "
                        f"Supported providers: {list(LLM_PROVIDERS.keys())}")

    return LLM_PROVIDERS[provider_name](**kwargs)

__all__ = [
    "get_qwen_llm",
    # "get_ollama_llm",  # 暂时禁用
    "get_openai_llm",
    # "get_cephalon_qwen_llm",  # 暂时禁用
    "get_llm_provider",
    "EnhancedQwenLLM",
    # "EnhancedOllamaLLM",  # 暂时禁用
    # "CephalonQwenLLM",  # 暂时禁用
    "LLM_PROVIDERS"
]