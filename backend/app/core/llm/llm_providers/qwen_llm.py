from langchain_community.chat_models.tongyi import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain.schema import BaseMessage, HumanMessage, AIMessage, SystemMessage
from typing import List, Dict, Optional, Any, AsyncGenerator
from loguru import logger
import dashscope
from dashscope.api_entities.dashscope_response import GenerationResponse

from ....core.config.settings import settings

class EnhancedQwenLLM(ChatTongyi):
    """
    增强的Qwen LLM，集成旧版本QwenClient的功能
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 设置DashScope API密钥
        if hasattr(self, 'api_key') and self.api_key:
            dashscope.api_key = str(self.api_key)
        logger.info("Enhanced Qwen LLM initialized with DashScope integration")

    def get_chat_response_direct(
        self,
        model: str,
        messages: List[Dict[str, str]]
    ) -> str:
        """
        直接使用DashScope SDK发起非流式聊天请求
        保持与旧版本QwenClient的兼容性

        Args:
            model (str): 要使用的模型名称
            messages (List[Dict[str, str]]): 对话消息列表

        Returns:
            str: 模型的文本响应
        """
        try:
            response: GenerationResponse = dashscope.Generation.call(
                model=model,
                messages=messages,
                result_format='message',
                stream=False
            )

            if response.status_code == 200:
                content = response.output.choices[0].message.content
                logger.info(f"DashScope response received successfully. Usage: {response.usage}")
                return content
            else:
                logger.error(f"Error from DashScope API. Status: {response.status_code}, Code: {response.code}, Message: {response.message}")
                return f"Error from DashScope: {response.message}"

        except Exception as e:
            logger.exception(f"An unexpected error occurred while communicating with DashScope: {e}")
            return f"An unexpected error occurred: {e}"

def get_qwen_llm(
    api_key: str = None,
    model: str = None,
    base_url: str = None,
    enhanced: bool = True,
    **kwargs
):
    """
    工厂函数：返回配置好的 Qwen LLM 实例

    Args:
        api_key: API密钥
        model: 模型名称
        base_url: API基础URL
        enhanced: 是否使用增强版本（集成旧版本功能）
        **kwargs: 其他参数
    """
    llm_class = EnhancedQwenLLM if enhanced else ChatTongyi

    return llm_class(
        api_key=api_key or settings.QIANWEN_API_KEY,
        model=model or settings.DEFAULT_QIANWEN_MODEL,
        **kwargs
    )