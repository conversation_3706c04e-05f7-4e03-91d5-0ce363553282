import re
import json
from loguru import logger
from typing import List, Optional

def _try_fix_common_json_errors(json_str: str) -> str:
    """
    尝试修正从LLM输出中提取的JSON字符串中常见的格式错误。
    - 移除单行 (//) 和多行 (/* */) 注释。
    - 移除对象和数组中尾随的逗号。

    Args:
        json_str (str): 可能包含错误的JSON字符串。

    Returns:
        str: 修复后的JSON字符串。
    """
    # 1. 移除 // 和 /* ... */ 形式的注释
    # 移除单行注释 // ...
    fixed_str = re.sub(r'//.*', '', json_str)
    # 移除多行注释 /* ... */
    fixed_str = re.sub(r'/\*.*?\*/', '', fixed_str, flags=re.DOTALL)

    # 2. 移除数组和对象中尾随的逗号
    # 例如: [1, 2, 3,] -> [1, 2, 3] or {"a":1, "b":2,} -> {"a":1, "b":2}
    # 使用正则表达式查找后面跟着空白和 `}` 或 `]` 的逗号，并将其移除。
    fixed_str = re.sub(r',\s*([}\]])', r'\1', fixed_str)
    
    return fixed_str.strip()

def clean_llm_json_response(response_str: str, required_keys: Optional[List[str]] = None) -> str:
    """
    清理LLM返回的可能包含Markdown标记的JSON字符串。
    它会尝试多种策略来提取一个有效的JSON字符串。
    1. 通过正则表达式寻找被 ```json ... ``` 包裹的代码块。
    2. 寻找由第一个 '{' 和最后一个 '}' 包围的子字符串。
    3. (兜底机制) 如果提供了 required_keys，则尝试从文本中提取键值对来重组JSON。

    Args:
        response_str (str): LLM返回的原始字符串。
        required_keys (Optional[List[str]]): 一个可选的键列表，用于在标准解析失败时，
                                             尝试从文本中提取这些键值对来重组JSON。

    Returns:
        str: 清理后的、可能可以被解析的JSON字符串。
    """
    if not isinstance(response_str, str):
        return ""
        
    # 策略1: 寻找被 ```json ... ``` 包裹的代码块
    match = re.search(r'```(?:json)?\s*(\{.*\})\s*```', response_str, re.DOTALL)
    if match:
        json_str = match.group(1)
        logger.info("通过正则表达式找到了JSON代码块。")
        try:
            # 在解析前尝试修复常见的格式错误
            fixed_json_str = _try_fix_common_json_errors(json_str)
            json.loads(fixed_json_str)
            return fixed_json_str
        except json.JSONDecodeError:
            logger.warning("从Markdown代码块中提取的字符串即使修复后也不是有效的JSON，将尝试其他策略。")

    # 策略2: 寻找第一个 '{' 和最后一个 '}'
    try:
        start_index = response_str.index('{')
        end_index = response_str.rindex('}') + 1
        json_str = response_str[start_index:end_index]
        # 在解析前尝试修复
        fixed_json_str = _try_fix_common_json_errors(json_str)
        json.loads(fixed_json_str) 
        logger.info("通过定位第一个和最后一个大括号找到了JSON。")
        return fixed_json_str
    except (ValueError, json.JSONDecodeError):
        logger.info("通过大括号定位JSON失败，将尝试兜底策略。")

    # 策略3: 兜底机制 - 基于键值对重组JSON
    if required_keys:
        logger.info(f"执行兜底策略：使用预设键 {required_keys} 尝试重组JSON。")
        extracted_pairs = []
        # 这个正则表达式尝试匹配一个键，后面跟着冒号，然后是一个值。
        # 值可以是：用双引号括起来的字符串、布尔值(true/false)、一个完整的对象{...}、或一个完整的列表[...]。
        # 它特别处理了转义的双引号 \" 在字符串值内部的情况。
        value_pattern = r'(?:true|false|"(?:\\.|[^"\\])*"|\{.*?\}|\[.*?\])'
        
        for key in required_keys:
            key_pattern = f'"{key}"\s*:\s*({value_pattern})'
            match = re.search(key_pattern, response_str, re.DOTALL)
            
            if match:
                value = match.group(1).strip()
                # 对捕获的对象和列表值进行额外检查，确保它们是平衡的
                if (value.startswith('{') and not value.endswith('}')) or \
                   (value.startswith('[') and not value.endswith(']')):
                    logger.warning(f"捕获到键 '{key}' 的值 '{value[:50]}...' 可能是截断的，跳过。")
                    continue

                extracted_pairs.append(f'"{key}": {value}')

        if extracted_pairs:
            reconstructed_json = "{" + ", ".join(extracted_pairs) + "}"
            logger.info(f"重组后的JSON(待验证): {reconstructed_json}")
            try:
                # 重组后的JSON也可能从value中带入问题，同样修复一下
                fixed_reconstructed_json = _try_fix_common_json_errors(reconstructed_json)
                json.loads(fixed_reconstructed_json)
                logger.info("成功通过兜底策略重组了有效的JSON。")
                return fixed_reconstructed_json
            except json.JSONDecodeError as e:
                logger.error(f"重组后的JSON仍然无效: {e}")

    logger.warning(f"所有JSON提取策略均失败，返回原始字符串。原文: '{response_str[:200]}...'")
    return response_str 