from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from loguru import logger
from .exceptions import APIException

async def api_exception_handler(request: Request, exc: APIException):
    """处理自定义API异常"""
    logger.error(
        "API Exception: {} - {}",
        exc.code,
        exc.message
    )
    
    # 如果有详细信息，将其添加到message中
    message = exc.message
    if exc.detail:
        if isinstance(exc.detail, (list, dict)):
            detail_str = str(exc.detail)
        else:
            detail_str = str(exc.detail)
        message = f"{message}：{detail_str}"
    
    return JSONResponse(
        status_code=exc.code,
        content={
            "code": exc.code,
            "message": message,
            "data": None
        }
    )

async def http_exception_handler(request: Request, exc: HTTPException):
    """处理FastAPI的HTTPException"""
    logger.error(
        "HTTP Exception: {} - {}",
        exc.status_code,
        exc.detail
    )
    
    # 获取异常详细信息
    error_message = str(exc.detail) if exc.detail else "请求处理失败"
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.status_code,
            "message": error_message,
            "data": None
        }
    )

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证异常"""
    logger.error(
        "Validation Error: {}",
        str(exc)
    )
    
    # 提取验证错误详情
    error_messages = []
    for error in exc.errors():
        field = ".".join(str(x) for x in error["loc"])
        error_messages.append(f"{field}: {error['msg']}")
    
    # 将所有错误信息组合成一个字符串
    error_message = "；".join(error_messages)
    
    response = JSONResponse(
        status_code=400,
        content={
            "code": 400,
            "message": f"请求参数验证失败：{error_message}",
            "data": None
        }
    )
    logger.info(f"Response type: {type(response).__name__}")
    return response

async def general_exception_handler(request: Request, exc: Exception):
    """处理其他所有异常"""
    logger.error(
        "General Exception: {}",
        str(exc)
    )
    
    # 获取异常详细信息
    error_message = str(exc)
    
    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": f"服务器内部错误：{error_message}",
            "data": None
        }
    )