from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import base64
import os
from typing import Union, Dict, Any
import json
from fastapi import HTTPException, status

class Encryption:
    def __init__(self, key: str, iv: str):
        """
        初始化加密工具类
        :param key: AES密钥（base64编码的32字节密钥）
        :param iv: 初始化向量（base64编码的16字节IV）
        """
        try:
            # 解码base64密钥和IV
            key_bytes = base64.b64decode(key)
            iv_bytes = base64.b64decode(iv)
            
            if len(key_bytes) != 32:
                raise ValueError("AES密钥必须是32字节")
            if len(iv_bytes) != 16:
                raise ValueError("初始化向量必须是16字节")
            
            self.key = key_bytes
            self.iv = iv_bytes
        except Exception as e:
            raise ValueError(f"密钥或IV格式错误: {str(e)}")

    def encrypt(self, data: Union[str, Dict[str, Any]]) -> str:
        """
        加密数据
        :param data: 要加密的数据（字符串或字典）
        :return: Base64编码的加密字符串
        """
        try:
            # 如果输入是字典，转换为JSON字符串
            if isinstance(data, dict):
                data = json.dumps(data, ensure_ascii=False)
            
            # 创建AES加密器
            cipher = AES.new(self.key, AES.MODE_CBC, self.iv)
            
            # 对数据进行填充和加密
            padded_data = pad(data.encode('utf-8'), AES.block_size)
            encrypted_data = cipher.encrypt(padded_data)
            
            # Base64编码
            return base64.b64encode(encrypted_data).decode('utf-8')
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"加密失败: {str(e)}"
            )

    def decrypt(self, encrypted_data: str) -> Union[str, Dict[str, Any]]:
        """
        解密数据
        :param encrypted_data: Base64编码的加密字符串
        :return: 解密后的数据（字符串或字典）
        """
        try:
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_data)
            
            # 创建AES解密器
            cipher = AES.new(self.key, AES.MODE_CBC, self.iv)
            
            # 解密和去除填充
            decrypted_data = unpad(cipher.decrypt(encrypted_bytes), AES.block_size)
            
            # 尝试解析JSON
            try:
                return json.loads(decrypted_data.decode('utf-8'))
            except json.JSONDecodeError:
                return decrypted_data.decode('utf-8')
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"解密失败: {str(e)}"
            )

    @staticmethod
    def generate_key_iv() -> tuple[str, str]:
        """
        生成AES密钥和初始化向量
        :return: (key, iv) 元组，key为base64编码的256位(32字节)密钥，iv为base64编码的128位(16字节)IV
        """
        # 生成256位(32字节)的密钥
        key = os.urandom(32)
        # 生成128位(16字节)的初始化向量
        iv = os.urandom(16)
        # 转换为base64编码的字符串
        return base64.b64encode(key).decode('utf-8'), base64.b64encode(iv).decode('utf-8') 