"""
JSON工具模块 - 提供自定义的JSON序列化功能
"""

import json
from datetime import datetime, date
from typing import Any, Dict, List, Union


class CustomJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime和其他Python对象"""
    
    def default(self, obj: Any) -> Union[str, Dict, List, Any]:
        """重写default方法处理特殊类型"""
        # 处理datetime类型
        if isinstance(obj, datetime):
            return obj.isoformat()
        
        # 处理date类型
        if isinstance(obj, date):
            return obj.isoformat()
            
        # 其他可序列化对象
        if hasattr(obj, 'to_dict') and callable(getattr(obj, 'to_dict')):
            return obj.to_dict()
            
        # 对于其他类型，使用默认行为
        return super().default(obj)


def json_dumps(obj: Any, **kwargs) -> str:
    """使用自定义的JSON编码器序列化对象"""
    kwargs.setdefault('cls', CustomJSONEncoder)
    kwargs.setdefault('ensure_ascii', False)
    return json.dumps(obj, **kwargs)


def json_loads(s: Union[str, bytes], **kwargs) -> Any:
    """反序列化JSON字符串"""
    return json.loads(s, **kwargs) 