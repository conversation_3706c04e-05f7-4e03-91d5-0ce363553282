from fastapi import HTTPException, status
from typing import Any

from app.schemas.response import ErrorResponse

class APIException(HTTPException):
    """API异常基类"""
    def __init__(
        self,
        code: int,
        message: str,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        detail: Any = None
    ):
        super().__init__(status_code=status_code)
        self.code = code
        self.message = message
        self.detail = detail

    def to_response(self) -> ErrorResponse:
        return ErrorResponse(
            code=self.code,
            message=self.message,
            detail=self.detail
        )

class ValidationError(APIException):
    """验证错误"""
    def __init__(self, message: str = "输入数据验证失败", detail: Any = None):
        super().__init__(
            code=1001,
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail
        )

class AuthenticationError(APIException):
    """认证错误"""
    def __init__(self, message: str = "认证失败", detail: Any = None):
        super().__init__(
            code=1002,
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail
        )

class AuthorizationError(APIException):
    """授权错误"""
    def __init__(self, message: str = "没有权限执行此操作", detail: Any = None):
        super().__init__(
            code=1003,
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )

class NotFoundError(APIException):
    """资源不存在错误"""
    def __init__(self, message: str = "请求的资源不存在", detail: Any = None):
        super().__init__(
            code=1004,
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail
        )

class ConflictError(APIException):
    """资源冲突错误"""
    def __init__(self, message: str = "资源已存在", detail: Any = None):
        super().__init__(
            code=1005,
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            detail=detail
        )

class RateLimitError(APIException):
    """请求频率限制错误"""
    def __init__(self, message: str = "请求过于频繁", detail: Any = None):
        super().__init__(
            code=1006,
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=detail
        )

class ServerError(APIException):
    """服务器内部错误"""
    def __init__(self, message: str = "服务器内部错误", detail: Any = None):
        super().__init__(
            code=1007,
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        ) 