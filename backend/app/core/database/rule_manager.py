from sqlalchemy.orm import Session
from sqlalchemy import func
from .models.novel import ChapterRegexPattern
from datetime import datetime
from typing import List, Dict, Optional

class RuleManager:
    """
    章节分割正则规则的管理器。
    负责规则的加载、分组、添加、命中统计、启用/禁用/删除等操作。
    所有规则均存储于 ChapterRegexPattern 表中。
    """
    def __init__(self, db: Session):
        """
        初始化规则管理器。
        Args:
            db (Session): SQLAlchemy 数据库会话。
        """
        self.db = db

    def load_rules(self, pattern_type: Optional[str] = None, only_active: bool = True) -> List[ChapterRegexPattern]:
        """
        加载规则列表。
        Args:
            pattern_type (str, 可选): 规则类型（intro/regular/anywhere），不传则加载全部类型。
            only_active (bool): 是否只加载启用的规则。
        Returns:
            List[ChapterRegexPattern]: 规则对象列表，按命中次数降序、创建时间升序排序。
        """
        query = self.db.query(ChapterRegexPattern)
        if only_active:
            query = query.filter(ChapterRegexPattern.is_active == True)
        if pattern_type:
            query = query.filter(ChapterRegexPattern.pattern_type == pattern_type)
        return query.order_by(ChapterRegexPattern.hit_count.desc(), ChapterRegexPattern.created_at.asc()).all()

    def get_grouped_rules(self, pattern_type: Optional[str] = None, group_size: int = 30) -> List[List[ChapterRegexPattern]]:
        """
        按组加载规则（如每30条一组），便于分批匹配。
        Args:
            pattern_type (str, 可选): 规则类型。
            group_size (int): 每组规则数量。
        Returns:
            List[List[ChapterRegexPattern]]: 分组后的规则列表。
        """
        rules = self.load_rules(pattern_type=pattern_type)
        return [rules[i:i+group_size] for i in range(0, len(rules), group_size)]

    def add_rule(self, pattern: str, pattern_type: str, source: str = 'manual', description: Optional[str] = None) -> ChapterRegexPattern:
        """
        添加新规则（避免重复）。
        Args:
            pattern (str): 正则表达式内容。
            pattern_type (str): 规则类型。
            source (str): 规则来源（init/llm/manual等）。
            description (str, 可选): 规则描述。
        Returns:
            ChapterRegexPattern: 新增或已存在的规则对象。
        """
        existing = self.db.query(ChapterRegexPattern).filter_by(pattern=pattern, pattern_type=pattern_type).first()
        if existing:
            if not existing.is_active:
                existing.is_active = True
                self.db.commit()
            return existing
        rule = ChapterRegexPattern(
            pattern=pattern,
            pattern_type=pattern_type,
            source=source,
            is_active=True,
            hit_count=0,
            created_at=datetime.utcnow(),
            last_hit_at=None
        )
        self.db.add(rule)
        self.db.commit()
        return rule

    MAX_HIT_COUNT = 1_000_000_000  # 10亿

    def record_hit(self, rule_id: int):
        """
        命中规则时，增加 hit_count 并更新时间。
        超过上限（10亿）不再自增。
        Args:
            rule_id (int): 规则ID。
        """
        rule = self.db.query(ChapterRegexPattern).filter_by(id=rule_id).first()
        if rule:
            if rule.hit_count is None or rule.hit_count < self.MAX_HIT_COUNT:
                rule.hit_count = (rule.hit_count or 0) + 1
                rule.last_hit_at = datetime.utcnow()
                self.db.commit()

    def disable_rule(self, rule_id: int):
        """
        禁用规则（is_active=False）。
        Args:
            rule_id (int): 规则ID。
        """
        rule = self.db.query(ChapterRegexPattern).filter_by(id=rule_id).first()
        if rule and rule.is_active:
            rule.is_active = False
            self.db.commit()

    def enable_rule(self, rule_id: int):
        """
        启用规则（is_active=True）。
        Args:
            rule_id (int): 规则ID。
        """
        rule = self.db.query(ChapterRegexPattern).filter_by(id=rule_id).first()
        if rule and not rule.is_active:
            rule.is_active = True
            self.db.commit()

    def delete_rule(self, rule_id: int):
        """
        删除规则。
        Args:
            rule_id (int): 规则ID。
        """
        rule = self.db.query(ChapterRegexPattern).filter_by(id=rule_id).first()
        if rule:
            self.db.delete(rule)
            self.db.commit()

    def get_rule_by_id(self, rule_id: int) -> Optional[ChapterRegexPattern]:
        """
        根据ID获取规则对象。
        Args:
            rule_id (int): 规则ID。
        Returns:
            ChapterRegexPattern or None
        """
        return self.db.query(ChapterRegexPattern).filter_by(id=rule_id).first() 