from sqlalchemy.orm import Session
from ..database.base import Base, db_manager
from loguru import logger
from ..config.settings import settings

# 导入所有模型以确保它们被注册到Base.metadata
def _import_all_models():
    """
    确保所有模型被导入
    这样SQLAlchemy才能在创建表时知道所有模型结构
    """
    # 导入用户和Token模型
    from ..database.models.user import User, AuthProvider
    from ..database.models.token import Token
    
    # 导入项目相关模型
    from ..database.models.project_models import Project, Character, StoryOutline, Scene, StoryFrame
    
    # 导入上传相关模型
    from ..database.models.upload import UploadSession, UploadChunk
    
    # 导入小说相关模型
    from ..database.models.novel import NovelDocument, NovelChapter, NovelEntity
    
    # 如果有更多模型，请在此处导入
    # from ..database.models.your_model import YourModel
    
    logger.info("所有数据库模型已导入")

def init_db() -> None:
    """
    初始化数据库，创建所有表
    """
    # 先确保所有模型被导入
    _import_all_models()
    
    # 然后初始化数据库
    db_manager.init_db()

def drop_db() -> None:
    """
    删除所有表（谨慎使用）
    """
    # 确保所有模型已加载
    _import_all_models()
    logger.info("删除数据库表...")
    # 删除所有表
    db_manager.drop_db()

def get_connection_info() -> dict:
    """
    获取数据库连接信息
    """
    return db_manager.get_connection_info()

if __name__ == "__main__":
    logger.info("开始创建数据库表...")
    init_db()
    logger.success("数据库初始化完成!") 