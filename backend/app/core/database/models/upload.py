from datetime import datetime
from sqlalchemy import J<PERSON><PERSON>, Column, Float, Integer, String, DateTime, Foreign<PERSON>ey, <PERSON>olean, Text
from sqlalchemy.orm import relationship
from ...database.base import Base

class UploadSession(Base):
    """文件上传会话"""
    __tablename__ = "upload_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String, nullable=False)  # 文件名
    file_size = Column(Integer, nullable=False)  # 文件总大小
    chunk_size = Column(Integer, nullable=False)  # 分块大小
    total_chunks = Column(Integer, nullable=False)  # 总分块数
    uploaded_chunks = Column(Integer, default=0)  # 已上传分块数
    temp_dir = Column(String, nullable=False)  # 临时存储目录
    is_completed = Column(Boolean, default=False)  # 是否完成
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    user_id = Column(Integer, ForeignKey("users.id"))
    
    # 关联关系
    user = relationship("User", back_populates="upload_sessions")
    chunks = relationship("UploadChunk", back_populates="session", cascade="all, delete-orphan")
    novel_document = relationship("NovelDocument", back_populates="upload_session", uselist=False)

class UploadChunk(Base):
    """上传的文件分块"""
    __tablename__ = "upload_chunks"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("upload_sessions.id"))
    chunk_index = Column(Integer, nullable=False)  # 分块序号
    chunk_hash = Column(String, nullable=False)  # 分块哈希值
    is_uploaded = Column(Boolean, default=False)  # 是否已上传
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    session = relationship("UploadSession", back_populates="chunks")