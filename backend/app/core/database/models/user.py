# Python 标准库导入
from datetime import datetime
import enum

# SQLAlchemy 相关导入
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

# 本地模块导入
from ...database.base import Base

class AuthProvider(str, enum.Enum):
    LOCAL = "local"
    GOOGLE = "google"
    GITHUB = "github"
    WECHAT = "wechat"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    nickname = Column(String, nullable=True)
    hashed_password = Column(String, nullable=True)  # 第三方登录可能没有密码
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    auth_provider = Column(Enum(AuthProvider), default=AuthProvider.LOCAL)
    provider_id = Column(String, unique=True, nullable=True)  # 第三方平台的用户ID
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    tokens = relationship("Token", back_populates="user", cascade="all, delete-orphan")
    upload_sessions = relationship("UploadSession", back_populates="user")

    def __repr__(self):
        return f"<User {self.username}>" 