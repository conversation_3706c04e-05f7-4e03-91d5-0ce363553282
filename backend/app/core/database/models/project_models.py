# models.py
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, create_engine
from sqlalchemy.orm import declarative_base, relationship
from datetime import datetime



# 项目表
# models.py
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, func
from sqlalchemy.orm import declarative_base, relationship
from datetime import timezone
# 本地模块导入
from ...database.base import Base

class Project(Base):
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    type = Column(String(255), nullable=True)
    # 创建时间，默认当前UTC时间
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    # 更新时间，默认当前UTC时间，更新时自动刷新
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    characters = relationship("Character", back_populates="project", cascade="all, delete")
    story_outline = relationship("StoryOutline", back_populates="project", uselist=False, cascade="all, delete")
    scenes = relationship("Scene", back_populates="project", cascade="all, delete") 
    story_frames = relationship("StoryFrame", back_populates="project", cascade="all, delete")

# 其他 Character、StoryOutline、StoryFrame 类不变，前面讲过

# 角色表
class Character(Base):
    __tablename__ = "characters"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    name = Column(String(255), nullable=False)
    role_type = Column(String(50), nullable=False)  # 主角/配角
    species = Column(String(255), nullable=True)
    trait = Column(String(255), nullable=True)
    age = Column(String(50), nullable=True)
    description = Column(Text, nullable=True)
    # 三视图图片地址
    front_view_url = Column(String(255), nullable=True)
    side_view_url = Column(String(255), nullable=True)
    back_view_url = Column(String(255), nullable=True)

    # 生成任务管理
    generate_task_id = Column(String(64), nullable=True)
    generate_status = Column(String(20), nullable=True)  # 'pending', 'processing', 'success', 'failed'


    project = relationship("Project", back_populates="characters")

# 故事大纲表
class StoryOutline(Base):
    __tablename__ = "story_outlines"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    theme = Column(Text, nullable=True)
    starting_point = Column(Text, nullable=True)
    trigger_event = Column(Text, nullable=True)
    emotional_reaction = Column(Text, nullable=True)
    growth_process = Column(Text, nullable=True)
    ending = Column(Text, nullable=True)

    project = relationship("Project", back_populates="story_outline")

    # 场景表
class Scene(Base):
    __tablename__ = "scenes"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    place = Column(String(255), nullable=True)
    time = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)

    project = relationship("Project", back_populates="scenes")


# 分镜表
class StoryFrame(Base):
    __tablename__ = "story_frames"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    scene = Column(String(255), nullable=True)
    time = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    frame_description = Column(Text, nullable=True)

    project = relationship("Project", back_populates="story_frames")
