from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean, Text, Float, JSON, Enum
from sqlalchemy.orm import relationship, Mapped, mapped_column
from ...database.base import Base
import enum

class ParseTaskType(enum.Enum):
    chapter_split = "chapter_split"
    content_clean = "content_clean"
    entity_extract = "entity_extract"
    relationship_analyze = "relationship_analyze"
    timeline_build = "timeline_build"
    plot_analyze = "plot_analyze"

class ParseTaskStatus(enum.Enum):
    pending = "pending"
    processing = "processing"
    completed = "completed"
    failed = "failed"

class NovelDocument(Base):
    """小说文档及解析状态"""
    __tablename__ = "novel_documents"
    
    id = Column(Integer, primary_key=True, index=True)
    upload_session_id = Column(Integer, ForeignKey("upload_sessions.id"), unique=True)
    title = Column(String, nullable=True)  # 小说标题
    author = Column(String, nullable=True)  # 作者
    
    # 解析状态
    task_id = Column(String, nullable=True, index=True)  # 关联的后台任务ID
    is_parsed = Column(Boolean, default=False)  # 是否已完全解析完成
    parse_status = Column(String, default="pending")  # pending, processing, completed, failed
    parse_progress = Column(Float, default=0.0)  # 解析进度 0-100%
    parse_error = Column(Text, nullable=True)  # 解析错误信息
    current_step = Column(String, default="not_started")  # 当前执行到的步骤
    
    # 解析步骤状态
    step_chapter_split = Column(Boolean, default=False)  # 步骤1: 章节分割
    step_chapter_split_at = Column(DateTime, nullable=True)  # 章节分割完成时间
    
    step_content_clean = Column(Boolean, default=False)  # 步骤2: 内容清洗
    step_content_clean_at = Column(DateTime, nullable=True)  # 内容清洗完成时间
    
    step_entity_extract = Column(Boolean, default=False)  # 步骤3: 实体提取
    step_entity_extract_at = Column(DateTime, nullable=True)  # 实体提取完成时间
    
    step_relationship_analyze = Column(Boolean, default=False)  # 步骤4: 关系分析
    step_relationship_analyze_at = Column(DateTime, nullable=True)  # 关系分析完成时间
    
    step_timeline_build = Column(Boolean, default=False)  # 步骤5: 时间线构建
    step_timeline_build_at = Column(DateTime, nullable=True)  # 时间线构建完成时间
    
    step_plot_analyze = Column(Boolean, default=False)
    step_plot_analyze_at = Column(DateTime, nullable=True)  # 情节分析完成时间
    
    # 解析时间
    parse_started_at = Column(DateTime, nullable=True)  # 解析开始时间
    parse_completed_at = Column(DateTime, nullable=True)  # 解析完成时间
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    upload_session = relationship("UploadSession", back_populates="novel_document")
    chapters = relationship("NovelChapter", back_populates="novel_document", cascade="all, delete-orphan")
    entities = relationship("NovelEntity", back_populates="novel_document", cascade="all, delete-orphan")
    timeline_events = relationship("NovelTimeline", back_populates="novel_document", cascade="all, delete-orphan")
    plots = relationship("NovelPlot", back_populates="novel_document", cascade="all, delete-orphan")
    fuzzy_chapter_logs = relationship("FuzzyChapterLog", back_populates="novel_document")
    parse_tasks = relationship("NovelParseTask", back_populates="novel", cascade="all, delete-orphan")

class NovelChapter(Base):
    """小说章节"""
    __tablename__ = "novel_chapters"
    
    id = Column(Integer, primary_key=True, index=True)
    novel_document_id = Column(Integer, ForeignKey("novel_documents.id"))
    chapter_index = Column(Integer, nullable=False)  # 章节序号
    title = Column(String, nullable=True)  # 章节标题
    content = Column(Text, nullable=False)  # 章节内容
    word_count = Column(Integer, default=0)  # 字数
    is_processed = Column(Boolean, default=False)  # 是否已处理
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    novel_document = relationship("NovelDocument", back_populates="chapters")

class NovelEntity(Base):
    """小说实体（角色、地点、时间等）"""
    __tablename__ = "novel_entities"
    
    id = Column(Integer, primary_key=True, index=True)
    novel_document_id = Column(Integer, ForeignKey("novel_documents.id"))
    entity_type = Column(String, nullable=False)  # character, location, time, event, etc.
    name = Column(String, nullable=False)  # 实体名称
    mentions = Column(Integer, default=0)  # 提及次数
    first_appearance = Column(Integer, nullable=True)  # 首次出现的章节
    attributes = Column(JSON, nullable=True)  # 实体属性和描述
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    novel_document = relationship("NovelDocument", back_populates="entities")
    source_relationships = relationship("NovelRelationship", foreign_keys="NovelRelationship.source_entity_id", back_populates="source_entity", cascade="all, delete-orphan")
    target_relationships = relationship("NovelRelationship", foreign_keys="NovelRelationship.target_entity_id", back_populates="target_entity", cascade="all, delete-orphan")

class NovelRelationship(Base):
    """小说实体之间的关系"""
    __tablename__ = "novel_relationships"
    
    id = Column(Integer, primary_key=True, index=True)
    source_entity_id = Column(Integer, ForeignKey("novel_entities.id"), nullable=False)
    target_entity_id = Column(Integer, ForeignKey("novel_entities.id"), nullable=False)
    relation_type = Column(String, nullable=False)  # 关系类型，如：friend, enemy, master, student, etc.
    attributes = Column(JSON, nullable=True)  # 关系的属性和描述
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    source_entity = relationship("NovelEntity", foreign_keys=[source_entity_id], back_populates="source_relationships")
    target_entity = relationship("NovelEntity", foreign_keys=[target_entity_id], back_populates="target_relationships")

class NovelTimeline(Base):
    """小说时间线事件"""
    __tablename__ = "novel_timelines"
    
    id = Column(Integer, primary_key=True, index=True)
    novel_document_id = Column(Integer, ForeignKey("novel_documents.id"), nullable=False)
    event_title = Column(String, nullable=False)  # 事件标题
    event_description = Column(Text, nullable=True)  # 事件描述
    chapter_index = Column(Integer, nullable=True)  # 发生的章节索引
    event_order = Column(Integer, nullable=False)  # 事件顺序
    event_time = Column(String, nullable=True)  # 事件发生的时间（文本描述）
    related_entities = Column(JSON, nullable=True)  # 相关实体的ID列表
    attributes = Column(JSON, nullable=True)  # 其他属性
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    novel_document = relationship("NovelDocument", back_populates="timeline_events")

class NovelPlot(Base):
    """小说情节分析"""
    __tablename__ = "novel_plots"
    
    id = Column(Integer, primary_key=True, index=True)
    novel_document_id = Column(Integer, ForeignKey("novel_documents.id"), nullable=False)
    plot_type = Column(String, nullable=False)  # 情节类型：主线、支线、伏笔、高潮等
    title = Column(String, nullable=False)  # 情节标题
    description = Column(Text, nullable=True)  # 情节描述
    start_chapter = Column(Integer, nullable=True)  # 开始章节
    end_chapter = Column(Integer, nullable=True)  # 结束章节
    importance = Column(Float, default=0.0)  # 重要性评分（0-10）
    related_entities = Column(JSON, nullable=True)  # 相关实体的ID列表
    related_events = Column(JSON, nullable=True)  # 相关事件的ID列表
    attributes = Column(JSON, nullable=True)  # 其他属性
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    novel_document = relationship("NovelDocument", back_populates="plots")

class FuzzyChapterLog(Base):
    """
    记录使用LLM分析模糊章节的历史。
    """
    __tablename__ = 'fuzzy_chapter_logs'

    id = Column(Integer, primary_key=True, index=True)
    novel_document_id = Column(Integer, ForeignKey('novel_documents.id'), nullable=False)
    analyzed_content_snippet = Column(Text, nullable=False)
    returned_regex = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    novel_document = relationship("NovelDocument", back_populates="fuzzy_chapter_logs") 

class NovelParseTask(Base):
    __tablename__ = "novel_parse_task"
    id = Column(Integer, primary_key=True, autoincrement=True)
    novel_id = Column(Integer, ForeignKey("novel_documents.id"), nullable=False, index=True)
    task_type = Column(Enum(ParseTaskType), nullable=False)
    status = Column(Enum(ParseTaskStatus), default=ParseTaskStatus.pending, nullable=False)
    progress = Column(Float, default=0.0)
    error = Column(Text, nullable=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    last_updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    novel = relationship("NovelDocument", back_populates="parse_tasks")

class ChapterRegexPattern(Base):
    """
    章节分割正则规则表。
    """
    __tablename__ = 'chapter_regex_patterns'

    id = Column(Integer, primary_key=True, index=True)
    pattern_type = Column(String(32), nullable=False)  # intro/regular/anywhere/other
    pattern = Column(Text, nullable=False)
    source = Column(String(32), nullable=False, default='init')  # init/llm/manual
    is_active = Column(Boolean, default=True)
    hit_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_hit_at = Column(DateTime, nullable=True) 

