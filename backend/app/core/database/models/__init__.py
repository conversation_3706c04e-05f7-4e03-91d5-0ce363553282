"""
数据库模型模块
提供系统所需的数据库模型定义，包括用户认证和令牌管理
"""

# 用户相关模型
from .user import User
from .token import Token

# 项目相关模型
from .project_models import Project, Character, StoryOutline, Scene, StoryFrame

# 上传相关模型
from .upload import UploadSession, UploadChunk

# 小说相关模型
from .novel import NovelDocument, NovelChapter, NovelEntity, NovelRelationship, NovelTimeline, NovelPlot, FuzzyChapterLog, ChapterRegexPattern

# 其他模型导入可以在这里添加
# from .your_model import YourModel

# 导出所有模型
__all__ = [
    # 用户相关模型
    'User',          # 用户模型
    'Token',         # 令牌模型
    
    # 项目相关模型
    'Project',       # 项目模型
    'Character',     # 角色模型
    'StoryOutline',  # 故事大纲模型
    'Scene',         # 场景模型
    'StoryFrame',    # 分镜模型
    
    # 上传相关模型
    'UploadSession', # 上传会话模型
    'UploadChunk',   # 上传块模型
    
    # 小说相关模型
    'NovelDocument', # 小说文档模型
    'NovelChapter',  # 小说章节模型
    'NovelEntity',   # 小说实体模型
    'NovelRelationship', # 小说关系模型
    'NovelTimeline', # 小说时间线模型
    'NovelPlot',     # 小说情节模型
    'FuzzyChapterLog', # 模糊章节日志模型
    'ChapterRegexPattern', # 章节分割正则规则表
    
    # 其他模型
    # 'YourModel',
] 