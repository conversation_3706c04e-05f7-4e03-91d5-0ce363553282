"""
数据库模块
提供数据库连接、会话管理、基础模型类和数据库初始化功能
"""

# 数据库基础组件
from .base import Base, get_db, engine, SessionLocal, db_manager

# 为了向后兼容，从database模块导入这些函数
# 它们实际上只是委托给db_manager的包装函数
from .database import init_db, drop_db, get_connection_info

__all__ = [
    # 基础组件
    'Base',         # SQLAlchemy 基础模型类
    'get_db',       # 数据库会话依赖函数
    'engine',       # 当前活动的数据库引擎
    'SessionLocal', # 数据库会话工厂
    'db_manager',   # 数据库管理器
    
    # 数据库管理
    'init_db',      # 初始化数据库表
    'drop_db',      # 删除所有数据库表（谨慎使用）
    'get_connection_info'  # 获取数据库连接信息
]
