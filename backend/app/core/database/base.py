# SQLAlchemy imports
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from loguru import logger
from typing import Generator, Dict, Any
from contextlib import contextmanager
from sqlalchemy import event
from sqlalchemy.inspection import inspect
from sqlalchemy.sql import text

# Local imports
from ..config.settings import settings

# 创建基础模型类
Base = declarative_base()

class DatabaseManager:
    """数据库管理器，负责创建和管理数据库引擎和会话"""
    
    def __init__(self):
        self._engines = {}
        self._session_factories = {}
        self._setup_engines()
    
    def _get_engine_args(self, db_type: str) -> Dict[str, Any]:
        """根据数据库类型返回适当的引擎参数"""
        # 基础配置
        args = {
            "pool_pre_ping": True,  # 处理断开的连接
        }
        
        # 根据数据库类型设置不同的配置
        if db_type == "sqlite":
            # SQLite 特定配置
            args.update({
                "connect_args": {"check_same_thread": False}  # 允许多线程访问
            })
        else:
            # PostgreSQL和其他数据库配置
            args.update({
                "pool_size": settings.DATABASE_POOL_SIZE,
                "max_overflow": settings.DATABASE_MAX_OVERFLOW
            })
        
        return args
    
    def _setup_engines(self):
        """初始化数据库引擎"""
        # 设置当前活动的数据库类型
        current_db_type = settings.DATABASE_TYPE
        logger.info("当前活动数据库类型: {}", current_db_type)
        
        # 创建当前活动数据库类型的引擎
        engine_args = self._get_engine_args(current_db_type)
        engine = create_engine(settings.DATABASE_URL, **engine_args)
        
        # 存储引擎和创建会话工厂
        self._engines[current_db_type] = engine
        self._session_factories[current_db_type] = sessionmaker(
            autocommit=False, 
            autoflush=False, 
            bind=engine
        )
        
        logger.info("数据库引擎已初始化: {}", current_db_type)
    
    @property
    def engine(self):
        """获取当前活动的数据库引擎"""
        return self._engines.get(settings.DATABASE_TYPE)
    
    @property
    def session_factory(self):
        """获取当前活动的会话工厂"""
        return self._session_factories.get(settings.DATABASE_TYPE)
    
    def get_db(self) -> Generator[Session, None, None]:
        """依赖注入用的会话获取器"""
        if not self.session_factory:
            raise RuntimeError(f"未找到数据库类型 '{settings.DATABASE_TYPE}' 的会话工厂")
        
        db = self.session_factory()
        try:
            yield db
        finally:
            db.close()
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """上下文管理器会话获取"""
        if not self.session_factory:
            raise RuntimeError(f"未找到数据库类型 '{settings.DATABASE_TYPE}' 的会话工厂")
        
        db = self.session_factory()
        try:
            yield db
        finally:
            db.close()
    
    def init_db(self) -> None:
        """初始化数据库，创建所有表"""
        current_db_type = settings.DATABASE_TYPE
        logger.info("正在初始化数据库 (类型: {})...", current_db_type)
        
        # 获取当前数据库引擎
        engine = self._engines.get(current_db_type)
        if not engine:
            raise RuntimeError(f"未找到数据库类型 '{current_db_type}' 的引擎")
        
        # 检查模型是否已经导入
        self._check_models_imported()
        
        try:
            # 根据数据库类型执行不同的初始化逻辑
            if current_db_type == "sqlite":
                # SQLite特定的初始化
                logger.info("使用SQLite特定的初始化逻辑")
                
                # 确保SQLite的外键约束被启用
                @event.listens_for(engine, "connect")
                def set_sqlite_pragma(dbapi_connection, connection_record):
                    cursor = dbapi_connection.cursor()
                    cursor.execute("PRAGMA foreign_keys=ON")
                    cursor.close()
                
                # 对于SQLite，我们可以直接使用SQLAlchemy的方法创建表
                # SQLite没有像PostgreSQL那样的schema概念
                Base.metadata.create_all(bind=engine)
                
                # 如果需要，可以执行一些SQLite特定的后续操作
                with engine.connect() as conn:
                    # 设置其他SQLite配置或执行特定操作
                    conn.execute(text("PRAGMA journal_mode=WAL;"))  # 使用WAL模式提高并发性能
                    conn.execute(text("PRAGMA synchronous=NORMAL;"))  # 减少同步开销，提高性能
                
                logger.success("SQLite数据库表创建成功!")
                
            elif current_db_type == "postgresql":
                # PostgreSQL特定的初始化
                logger.info("使用PostgreSQL特定的初始化逻辑")
                
                # 检查schema是否存在，如果不存在则创建
                inspector = inspect(engine)
                with engine.begin() as conn:
                    # 检查是否有public schema以外的schema需要创建
                    # 这里假设我们使用'app_schema'作为自定义schema
                    schema_name = 'app_schema'
                    if schema_name != 'public' and schema_name not in inspector.get_schema_names():
                        conn.execute(text(f'CREATE SCHEMA IF NOT EXISTS {schema_name}'))
                        logger.info(f"创建了PostgreSQL schema: {schema_name}")
                
                # 创建表
                Base.metadata.create_all(bind=engine)
                
                # PostgreSQL特定的后续操作
                with engine.begin() as conn:
                    # 设置表的所有者或权限等
                    # conn.execute(text("ALTER TABLE some_table OWNER TO app_user"))
                    pass
                
                logger.success("PostgreSQL数据库表创建成功!")
                
            else:
                # 默认初始化逻辑
                logger.info(f"使用默认初始化逻辑 (数据库类型: {current_db_type})")
                # 对于其他数据库类型，我们依赖SQLAlchemy的通用方法
                Base.metadata.create_all(bind=engine)
                logger.success(f"{current_db_type}数据库表创建成功!")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            # 根据需要可以选择重新抛出异常或者返回错误状态
            raise RuntimeError(f"数据库初始化失败: {str(e)}") from e
    
    def drop_db(self) -> None:
        """删除所有表（谨慎使用）"""
        logger.warning("正在删除所有数据库表 (类型: {})...", settings.DATABASE_TYPE)
        if not self.engine:
            raise RuntimeError(f"未找到数据库类型 '{settings.DATABASE_TYPE}' 的引擎")
        
        Base.metadata.drop_all(bind=self.engine)
        logger.warning("所有数据库表已删除!")
    
    def get_connection_info(self) -> dict:
        """获取当前数据库连接信息"""
        return {
            "type": settings.DATABASE_TYPE,
            "url": settings.DATABASE_URL,
            "engine": self.engine is not None
        }

    def _check_models_imported(self):
        """检查必要的模型是否已经导入"""
        expected_tables = ["users", "tokens", "projects", "characters", "story_outlines", "scenes", "story_frames"]
        registered_tables = set(Base.metadata.tables.keys())
        
        missing_tables = [table for table in expected_tables if table not in registered_tables]
        
        if missing_tables:
            logger.warning("发现未导入的模型表: {}", missing_tables)
            # 尝试动态导入
            try:
                from ..database.models.user import User, AuthProvider
                from ..database.models.token import Token
                from ..database.models.project_models import Project, Character, StoryOutline, Scene, StoryFrame
                logger.info("已动态导入模型")
                
                # 检查是否成功导入
                if set(missing_tables).issubset(set(Base.metadata.tables.keys())):
                    logger.success("所有模型现已成功导入")
                else:
                    still_missing = [table for table in missing_tables if table not in Base.metadata.tables.keys()]
                    logger.error("仍有模型未导入: {}", still_missing)
            except ImportError as e:
                logger.error("导入模型时出错: {}", str(e))
                raise RuntimeError(f"无法导入所需模型: {str(e)}")
        else:
            logger.info("所有必要的模型表已导入: {}", ", ".join(registered_tables))

# 创建数据库管理器实例
db_manager = DatabaseManager()

# 向外界暴露常用对象和函数
engine = db_manager.engine
get_db = db_manager.get_db
SessionLocal = db_manager.session_factory 