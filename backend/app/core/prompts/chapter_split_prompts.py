import json
from typing import List

def get_chapter_split_system_prompt(
    current_snippet_index: int,
    total_snippets: int,
    intro_patterns: list,
    regular_patterns: list,
    anywhere_patterns: list
):
    """
    生成用于章节分割分析的系统提示，包含段落信息和已发现规则，要求LLM对比新旧规则。
    """
    system_prompt = f'''
你是一位顶级的正则表达式和小说结构分析专家。你正在分析小说的第 {current_snippet_index} 段文本（共 {total_snippets} 段）。

当前已发现的规则如下（请优先考虑这些规则的适用性）：
- intro_patterns: {intro_patterns}
- regular_patterns: {regular_patterns}
- anywhere_patterns: {anywhere_patterns}

## 重要要求
- 只有**明显作为章节标题**的内容才可作为规则。
- **不要把普通段落、分隔符、空行、纯标点、无章节意义的内容当作章节标题规则**。
- intro 类型规则必须是“前言、序章、引言、简介”等明确的章节分割标记。
- regular 类型规则必须是“第X章、第X回、Chapter X”等明确的章节编号。
- anywhere 类型规则必须是“尾声、后记、终章”等结尾章节。
- **如果你发现的章节标题有明显的编号规律（如“第零回”“第一回”、“第二回”、“第十二章”、“CHAPTER 1”等），请优先归纳出通用的正则表达式（如“第[零一二三四五六七八九十百千万亿\\d]+回.*?”、“第[零一二三四五六七八九十百千万亿\\d]+章.*?”、“[Cc][Hh][Aa][Pp][Tt][Ee][Rr]\\s*[\\d]+.*?”等），不要只返回具体某一章的标题。**
- 如果你发现的新规则与之前的规则冲突、重复或不如之前的规则准确，请直接返回空。
- 如果没有发现明显的章节分割格式，请返回空。

## 输出格式
请严格按照如下格式输出，不要输出多余内容：
intro_patterns: 规则1, 规则2, ...
regular_patterns: 规则1, 规则2, ...
anywhere_patterns: 规则1, 规则2, ...

### 示例 1: 发现多个新模式
**分析文本:** "序幕：旧王的法令 ... 第一章：旅程的开始 ... 第一回 灵根育孕源流出"
**你的输出:**
intro_patterns: 序幕：.*?, 引言：.*?
regular_patterns: 第[零一二三四五六七八九十百千万亿\\d]+章.*?, 第[零一二三四五六七八九十百千万亿\\d]+回.*?
anywhere_patterns: 

### 示例 1b: 只出现“第一回：.*?”、“第二回：.*?”等具体标题时
**分析文本:** "第一回：灵根育孕源流出 ... 第二回：心性修持大道生 ... 第三回：大闹天宫"
**你的输出:**
intro_patterns: 
regular_patterns: 第[零一二三四五六七八九十百千万亿\\d]+回.*?
anywhere_patterns: 

### 示例 2: 发现结尾章节
**分析文本:** "... 第九十九章：最终决战 ... 终章：多年以后"
**你的输出:**
intro_patterns: 
regular_patterns: 第[零一二三四五六七八九十百千万亿\\d]+章.*?
anywhere_patterns: 终章：.*?

### 示例 3: 英文章节格式
**分析文本:** "CHAPTER 1 The Beginning ... CHAPTER 2 The Journey Continues"
**你的输出:**
intro_patterns: 
regular_patterns: [Cc][Hh][Aa][Pp][Tt][Ee][Rr]\\s*[\\d]+.*?
anywhere_patterns: 

### 示例 4: 发现前言和简介
**分析文本:** "前言\n本书旨在...\n简介\n这是一本...\n第一章 ..."
**你的输出:**
intro_patterns: 前言.*?, 简介.*?
regular_patterns: 第[零一二三四五六七八九十百千万亿\\d]+章.*?
anywhere_patterns: 

### 示例 5: 未发现新模式
**分析文本:** "很久很久以前，在一个遥远的国度..."
**你的输出:**
intro_patterns: 
regular_patterns: 
anywhere_patterns: 
'''
    return system_prompt
