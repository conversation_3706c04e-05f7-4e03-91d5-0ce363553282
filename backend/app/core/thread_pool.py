from concurrent.futures import Thread<PERSON>oolExecutor
import queue
import threading

# 通用线程池
general_thread_pool = ThreadPoolExecutor(max_workers=5)
# 解析专用线程池
parse_thread_pool = ThreadPoolExecutor(max_workers=3)
# IO 专用线程池
io_thread_pool = ThreadPoolExecutor(max_workers=5)

# 线程安全的结果队列
result_queue = queue.Queue()
parse_result_queue = queue.Queue()
io_result_queue = queue.Queue()

# 线程间通信工具
shutdown_event = threading.Event()
thread_lock = threading.Lock()

def submit_task(func, *args, **kwargs):
    """
    提交任务到通用线程池，返回 future 对象。
    任务完成后自动将结果放入 result_queue。
    """
    future = general_thread_pool.submit(func, *args, **kwargs)
    def callback(fut):
        try:
            result = fut.result()
            result_queue.put(result)
        except Exception as e:
            result_queue.put(e)
    future.add_done_callback(callback)
    return future

def submit_to_parse_pool(func, *args, **kwargs):
    """
    提交任务到解析线程池，返回 future 对象。
    任务完成后自动将结果放入 parse_result_queue。
    """
    future = parse_thread_pool.submit(func, *args, **kwargs)
    def callback(fut):
        try:
            result = fut.result()
            parse_result_queue.put(result)
        except Exception as e:
            parse_result_queue.put(e)
    future.add_done_callback(callback)
    return future

def submit_to_io_pool(func, *args, **kwargs):
    """
    提交任务到 IO 线程池，返回 future 对象。
    任务完成后自动将结果放入 io_result_queue。
    """
    future = io_thread_pool.submit(func, *args, **kwargs)
    def callback(fut):
        try:
            result = fut.result()
            io_result_queue.put(result)
        except Exception as e:
            io_result_queue.put(e)
    future.add_done_callback(callback)
    return future

def shutdown():
    """
    优雅关闭所有线程池，并通知所有等待线程。
    """
    general_thread_pool.shutdown(wait=True)
    parse_thread_pool.shutdown(wait=True)
    io_thread_pool.shutdown(wait=True)
    shutdown_event.set() 