from pydantic_settings import BaseSettings
from typing import Optional, List, Literal
import os
from dotenv import load_dotenv
from loguru import logger

# 获取当前环境
ENV = os.getenv("ENV", "development")
logger.info("当前环境: {}", ENV)

# 根据环境加载对应的.env文件
env_files = {
    "development": ".env.development",
    "testing": ".env.testing",
    "staging": ".env.staging",
    "production": ".env.production"
}

# 获取环境文件路径
env_file = env_files.get(ENV, ".env.development")
logger.info("使用环境配置文件: {}", env_file)

# 获取环境文件的绝对路径
env_file_abs = os.path.abspath(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), env_file))
logger.info("环境配置文件绝对路径: {}", env_file_abs)

# 加载环境变量
if os.path.exists(env_file_abs):
    load_dotenv(env_file_abs)
    logger.info("环境变量文件加载成功")
else:
    logger.warning("环境变量文件不存在: {}", env_file_abs)

class Settings(BaseSettings):
    # 基础配置
    ENVIRONMENT: str = ENV
    DEBUG: bool = True
    PROJECT_NAME: str = "AI绘本与短视频创作平台"
    VERSION: str = "0.1.0"
    API_V1_STR: str = "/api/v1"
    
    # 服务器配置
    HOST: str = "127.0.0.1"
    PORT: int = 8080
    WORKERS: int = 1
    
    # 数据库配置
    DATABASE_URL: str
    DATABASE_TYPE: Literal["sqlite", "postgresql"] = "postgresql"  # 数据库类型
    SQLITE_URL: Optional[str] = "sqlite:///./app.db"  # SQLite URL
    DATABASE_POOL_SIZE: int = 5
    DATABASE_MAX_OVERFLOW: int = 10
    INIT_DB: bool = False  # 是否在启动时初始化数据库
    
    # Redis配置
    REDIS_URL: Optional[str] = None
    REDIS_POOL_SIZE: int = 10
    
    # JWT配置
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # AI服务配置
    OLLAMA_API_BASE: str = "http://localhost:11434"
    DEFAULT_OLLAMA_MODEL: str = "qwen3:4b"
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_API_BASE: str = "https://api.openai.com/v1"
    QIANWEN_API_KEY: Optional[str] = None
    QIANWEN_API_BASE: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    DEFAULT_AI_SERVICE: Literal["openai", "qianwen", "ollama"] = "qianwen"
    DEFAULT_OPENAI_MODEL: str = "gpt-4-turbo-preview"
    DEFAULT_QIANWEN_MODEL: str = "qwen-plus"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    
    # 安全配置
    ALLOWED_HOSTS: List[str] = ["*"]
    CORS_ORIGINS: List[str] = ["*"]
    RATE_LIMIT: int = 100
    
    # 监控配置
    SENTRY_DSN: Optional[str] = None
    NEW_RELIC_LICENSE_KEY: Optional[str] = None

    # AES加密配置
    AES_KEY: str  # 32字节的AES密钥
    AES_IV: str   # 16字节的初始化向量
    AES_ENCRYPTION_VERSION: int = 1  # 加密版本号
    AES_ENCRYPTION_ENABLED: bool = True  # 是否启用加密
    AES_EXCLUDE_PATHS: List[str] = [  # 不需要加密的路径
        "/docs",
        "/redoc",
        "/api/v1/openapi.json",
        "/api/v1/upload/textfile",     # 普通文件上传
        "/api/v1/upload/init",         # 分块上传初始化
        "/api/v1/upload/chunk/",       # 分块上传 (包含session_id参数)
        "/api/v1/upload/status/"       # 上传状态查询 (包含session_id参数)
    ]

    # 文件上传配置
    UPLOAD_DIR: str = os.path.abspath("uploads")  # 文件上传目录
    MAX_UPLOAD_SIZE: int = 500 * 1024 * 1024  # 最大上传大小（500MB）
    ALLOWED_UPLOAD_EXTENSIONS: List[str] = [".txt", ".md", ".log", ".csv", ".json", ".xml"]  # 允许的文件扩展名

    CEPHALONQWEN_API_KEY: Optional[str] = None  # Cephalon Qwen API 密钥，建议通过环境变量设置
    CEPHALONQWEN_API_BASE: str = "https://cephalon.cloud/user-center/v1/model"  # Cephalon Qwen API 基础地址

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 根据环境和配置选择合适的数据库URL
        self._setup_database_url()
        
        logger.info("配置初始化完成")
        logger.info("服务器配置: HOST={}, PORT={}, WORKERS={}", self.HOST, self.PORT, self.WORKERS)
        logger.info("数据库配置: TYPE={}, URL={}", self.DATABASE_TYPE, self.DATABASE_URL)
        logger.info("JWT配置: ALGORITHM={}", self.ALGORITHM)
        logger.info("AI服务: {}", self.DEFAULT_AI_SERVICE)
        logger.info("加密功能: {}", "启用" if self.AES_ENCRYPTION_ENABLED else "禁用")

    def _setup_database_url(self):
        """根据环境设置数据库URL"""
        # 根据环境自动选择数据库类型（如果没有显式设置）
        if self.is_development() and os.getenv("DATABASE_TYPE") is None:
            self.DATABASE_TYPE = "sqlite"
        
        # 根据数据库类型设置连接URL
        if self.DATABASE_TYPE == "sqlite":
            # 确保SQLite URL是设置的
            if not self.SQLITE_URL:
                sqlite_path = os.path.abspath(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "app.db"))
                self.SQLITE_URL = f"sqlite:///{sqlite_path}"
            
            # 更新数据库URL为SQLite URL
            self.DATABASE_URL = self.SQLITE_URL
            logger.info("使用SQLite数据库: {}", self.DATABASE_URL)
        else:
            # 确保其他情况下使用的是原来的PostgreSQL连接
            logger.info("使用PostgreSQL数据库")

    class Config:
        case_sensitive = True
        env_file = env_file_abs
        env_file_encoding = 'utf-8'
        extra = 'allow'  # 允许额外的字段

    def is_development(self) -> bool:
        return self.ENVIRONMENT == "development"

    def is_testing(self) -> bool:
        return self.ENVIRONMENT == "testing"

    def is_staging(self) -> bool:
        return self.ENVIRONMENT == "staging"

    def is_production(self) -> bool:
        return self.ENVIRONMENT == "production"

    def get_encryption_config(self) -> dict:
        """获取加密配置"""
        return {
            "key": self.AES_KEY,
            "iv": self.AES_IV,
            "version": self.AES_ENCRYPTION_VERSION,
            "enabled": self.AES_ENCRYPTION_ENABLED,
            "exclude_paths": set(self.AES_EXCLUDE_PATHS)
        }

# 创建settings实例
settings = Settings() 