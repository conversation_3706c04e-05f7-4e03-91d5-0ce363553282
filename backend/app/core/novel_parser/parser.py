"""
小说解析器主类

这个类负责协调小说解析的各个步骤，根据解析进度调用相应的处理模块。
"""

import asyncio
from typing import Dict, Any, Optional, Generator
from datetime import datetime
from pathlib import Path
from loguru import logger
from sqlalchemy.orm import Session

from ...core.database.models import NovelDocument,  UploadSession
from ...core.novel_parser.parse_enums import ParseStep,ParseStatus
from .chapter_split import ChapterSplitter
from .content_clean import ContentCleaner
from .entity_extract import EntityExtractor
from .relationship_analyze import RelationshipAnalyzer
from .timeline_build import TimelineBuilder
from .plot_analyze import PlotAnalyzer


class NovelParser:
    """小说解析器，协调各个解析步骤的执行"""
    
    def __init__(self, db: Session, novel_id: int, chunk_size: int = 1024 * 1024):
        """
        初始化解析器
        
        Args:
            db: 数据库会话
            novel_id: 小说ID
            chunk_size: 文件读取块大小，默认1MB
        """
        self.db = db
        self.novel_id = novel_id
        self.novel_document = None
        self.file_path = None
        self.chunk_size = chunk_size
        
        # 初始化各个步骤的处理器
        self.chapter_splitter = ChapterSplitter(db)
        self.content_cleaner = ContentCleaner(db)
        self.entity_extractor = EntityExtractor(db)
        self.relationship_analyzer = RelationshipAnalyzer(db)
        self.timeline_builder = TimelineBuilder(db)
        self.plot_analyzer = PlotAnalyzer(db)
    
    def read_file_chunks(self) -> Generator[str, None, None]:
        """
        分块读取文件内容的生成器
        
        Yields:
            str: 文件内容块
        """
        with open(self.file_path, 'r', encoding='utf-8') as file:
            while True:
                chunk = file.read(self.chunk_size)
                if not chunk:
                    break
                yield chunk

    def initialize(self) -> bool:
        """
        初始化解析过程，加载小说文档和文件
        
        Returns:
            bool: 初始化是否成功
        """
        from ...core.config import settings
        
        try:
            # 获取小说文档
            self.novel_document = self.db.query(NovelDocument).filter(
                NovelDocument.id == self.novel_id
            ).first()
            
            if not self.novel_document:
                logger.error(f"小说解析初始化失败 - 找不到小说文档ID: {self.novel_id}")
                return False
            
            # 更新解析状态
            self.novel_document.parse_status = ParseStatus.PROCESSING
            self.novel_document.parse_started_at = datetime.utcnow()
            self.novel_document.current_step = ParseStep.CHAPTER_SPLIT
            self.db.commit()
            
            # 获取上传会话
            upload_session = self.db.query(UploadSession).filter(
                UploadSession.id == self.novel_document.upload_session_id
            ).first()
            
            if not upload_session:
                logger.error(f"小说解析初始化失败 - 找不到上传会话ID: {self.novel_document.upload_session_id}")
                self._update_failure("找不到上传会话")
                return False
            
            # 获取文件路径
            UPLOAD_DIR = Path(settings.UPLOAD_DIR)
            self.file_path = UPLOAD_DIR / upload_session.filename
            
            if not self.file_path.exists():
                logger.error(f"小说解析初始化失败 - 找不到文件: {self.file_path}")
                self._update_failure("找不到上传文件")
                return False
            
            return True
            
        except Exception as e:
            logger.exception(f"小说解析初始化异常: {str(e)}")
            self._update_failure(f"初始化失败: {str(e)}")
            return False
    
    def parse(self) -> bool:
        """
        执行解析过程（同步）
        
        Returns:
            bool: 解析是否成功
        """
        try:
            # 初始化
            if not self.initialize():
                return False
            
            # 根据当前步骤执行相应的解析
            current_step = self.novel_document.current_step
            
            # 执行章节分割
            if current_step <= ParseStep.CHAPTER_SPLIT:
                if not self._execute_step(
                    step=ParseStep.CHAPTER_SPLIT,
                    step_name="章节分割",
                    handler=self.chapter_splitter.process,
                    progress=20
                ):
                    return False
            
            # 执行内容清洗
            if current_step <= ParseStep.CONTENT_CLEAN:
                if not self._execute_step(
                    step=ParseStep.CONTENT_CLEAN,
                    step_name="内容清洗",
                    handler=self.content_cleaner.process,
                    progress=40
                ):
                    return False
            
            # # 执行实体提取
            # if current_step <= ParseStep.ENTITY_EXTRACT:
            #     if not self._execute_step(
            #         step=ParseStep.ENTITY_EXTRACT,
            #         step_name="实体提取",
            #         handler=self.entity_extractor.process,
            #         progress=60
            #     ):
            #         return False
            
            # # 执行关系分析
            # if current_step <= ParseStep.RELATIONSHIP_ANALYZE:
            #     if not self._execute_step(
            #         step=ParseStep.RELATIONSHIP_ANALYZE,
            #         step_name="关系分析",
            #         handler=self.relationship_analyzer.process,
            #         progress=80
            #     ):
            #         return False
            
            # # 执行时间线构建
            # if current_step <= ParseStep.TIMELINE_BUILD:
            #     if not self._execute_step(
            #         step=ParseStep.TIMELINE_BUILD,
            #         step_name="时间线构建",
            #         handler=self.timeline_builder.process,
            #         progress=90
            #     ):
            #         return False
            
            # # 执行情节分析
            # if current_step <= ParseStep.PLOT_ANALYZE:
            #     if not self._execute_step(
            #         step=ParseStep.PLOT_ANALYZE,
            #         step_name="情节分析",
            #         handler=self.plot_analyzer.process,
            #         progress=100
            #     ):
            #         return False
            
            # 完成解析
            # self.novel_document.parse_status = ParseStatus.COMPLETED
            # self.novel_document.is_parsed = True
            # self.novel_document.parse_completed_at = datetime.utcnow()
            # self.novel_document.current_step = ParseStep.COMPLETED
            # self.db.commit()
            
            logger.info(f"小说解析任务完成 - ID: {self.novel_id}")
            return True
            
        except Exception as e:
            logger.exception(f"小说解析任务异常: {str(e)}")
            self._update_failure(f"解析过程异常: {str(e)}")
            return False
    
    def _execute_step(
        self, 
        step: ParseStep, 
        step_name: str, 
        handler: callable, 
        progress: int
    ) -> bool:
        """
        执行单个解析步骤
        
        Args:
            step: 步骤枚举值
            step_name: 步骤名称
            handler: 处理函数
            progress: 完成后的进度值
            
        Returns:
            bool: 步骤是否成功执行
        """
        logger.info(f"解析小说 - ID: {self.novel_id}, 步骤: {step_name}")
        
        try:
            # 执行步骤处理，传入文件内容生成器
            handler(self.novel_document, self.read_file_chunks)
            
            # 更新步骤状态
            setattr(self.novel_document, f"step_{step.name.lower()}", True)
            setattr(self.novel_document, f"step_{step.name.lower()}_at", datetime.utcnow())
            self.novel_document.parse_progress = progress
            
            # 如果不是最后一个步骤，更新当前步骤为下一个步骤
            if step != ParseStep.PLOT_ANALYZE:
                # 获取所有步骤
                steps = list(ParseStep)
                # 找到当前步骤的索引
                current_index = steps.index(step)
                # 设置为下一个步骤
                self.novel_document.current_step = steps[current_index + 1]
            
            self.db.commit()
            return True
            
        except Exception as e:
            logger.exception(f"{step_name}失败 - ID: {self.novel_id}, 错误: {str(e)}")
            self._update_failure(f"{step_name}失败: {str(e)}")
            return False
    
    def _update_failure(self, error_message: str) -> None:
        """
        更新解析失败状态
        
        Args:
            error_message: 错误信息
        """
        try:
            self.novel_document.parse_status = ParseStatus.FAILED
            self.novel_document.parse_error = error_message
            self.db.commit()
        except Exception as e:
            logger.exception(f"更新解析失败状态异常: {str(e)}") 