"""
内容清洗模块

负责清理小说文本中的各种问题，如多余空格、排版问题、标点符号等。
"""

import re
import asyncio
from typing import List
from loguru import logger
from sqlalchemy.orm import Session

from ...core.database.models import NovelDocument, NovelChapter


class ContentCleaner:
    """内容清洗处理器"""
    
    def __init__(self, db: Session):
        """
        初始化处理器
        
        Args:
            db: 数据库会话
        """
        self.db = db
    
    def process(self, novel_document: NovelDocument, content: str = None) -> bool:
        """
        处理内容清洗
        
        Args:
            novel_document: 小说文档对象
            content: 小说文本内容（不使用，直接从数据库获取章节）
            
        Returns:
            bool: 处理是否成功
        """
        logger.info(f"开始内容清洗 - 小说ID: {novel_document.id}")
        
        try:
            # 获取所有章节
            chapters = self.db.query(NovelChapter).filter(
                NovelChapter.novel_document_id == novel_document.id
            ).order_by(NovelChapter.chapter_index).all()
            
            if not chapters:
                logger.warning(f"内容清洗 - 找不到章节 - 小说ID: {novel_document.id}")
                return True  # 没有章节也算成功
            
            # 清洗每个章节
            for chapter in chapters:
                self._clean_chapter(chapter)
            
            # 提交事务
            self.db.commit()
            
            logger.info(f"内容清洗完成 - 小说ID: {novel_document.id}, 共{len(chapters)}章")
            return True
            
        except Exception as e:
            logger.exception(f"内容清洗异常 - 小说ID: {novel_document.id}, 错误: {str(e)}")
            raise
    
    def _clean_chapter(self, chapter: NovelChapter) -> None:
        """
        清洗单个章节的内容
        
        Args:
            chapter: 章节对象
        """
        # 获取原始内容
        content = chapter.content
        
        # 1. 去除多余的空白字符
        content = self._remove_extra_whitespace(content)
        
        # 2. 统一标点符号
        content = self._normalize_punctuation(content)
        
        # 3. 修复排版问题
        content = self._fix_formatting(content)
        
        # 4. 去除特殊字符和HTML标签
        content = self._remove_special_chars(content)
        
        # 更新章节内容
        chapter.content = content
        
        # 更新字数统计
        chapter.word_count = len(content)
    
    def _remove_extra_whitespace(self, text: str) -> str:
        """
        去除多余的空白字符
        
        Args:
            text: 文本内容
            
        Returns:
            str: 处理后的文本
        """
        # 将多个空格替换为单个空格
        text = re.sub(r'\s+', ' ', text)
        
        # 去除每行开头和结尾的空白
        lines = text.split('\n')
        lines = [line.strip() for line in lines]
        
        # 去除连续的空行
        result_lines = []
        prev_empty = False
        for line in lines:
            if not line:
                if not prev_empty:
                    result_lines.append(line)
                prev_empty = True
            else:
                result_lines.append(line)
                prev_empty = False
        
        return '\n'.join(result_lines)
    
    def _normalize_punctuation(self, text: str) -> str:
        """
        统一标点符号
        
        Args:
            text: 文本内容
            
        Returns:
            str: 处理后的文本
        """
        # 统一中文引号
        text = text.replace('"', '“').replace('"', '”')
        text = text.replace('‘', '‘').replace('’', '’')
        
        # 统一省略号
        text = re.sub(r'\.{3,}', '……', text)
        
        # 统一破折号
        text = re.sub(r'-{2,}', '——', text)
        
        # 其他标点符号统一
        punctuation_map = {
            # 中文标点
            '，': '，', '。': '。', '！': '！', '？': '？', '；': '；', '：': '：',
            '（': '（', '）': '）', '【': '【', '】': '】', '「': '「', '」': '」',
            '『': '『', '』': '』', '《': '《', '》': '》', '“': '“', '”': '”',
            '‘': '‘', '’': '’', '·': '·',
            # 英文标点转中文
            ',': '，', '.': '。', '!': '！', '?': '？', ';': '；', ':': '：',
            '(': '（', ')': '）', '[': '【', ']': '】', '{': '【', '}': '】',
            '"': '“', "'": "‘",
        }
        
        for old, new in punctuation_map.items():
            text = text.replace(old, new)
        
        return text
    
    def _fix_formatting(self, text: str) -> str:
        """
        修复排版问题
        
        Args:
            text: 文本内容
            
        Returns:
            str: 处理后的文本
        """
        # 确保段落之间有一个空行
        text = re.sub(r'([^\n])\n([^\n])', r'\1\n\n\2', text)
        
        # 去除段落开头的空格
        text = re.sub(r'\n\s+', '\n', text)
        
        return text
    
    def _remove_special_chars(self, text: str) -> str:
        """
        去除特殊字符和HTML标签
        
        Args:
            text: 文本内容
            
        Returns:
            str: 处理后的文本
        """
        # 去除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 去除控制字符
        text = re.sub(r'[\x00-\x1F\x7F]', '', text)
        
        # 去除特殊Unicode字符
        text = re.sub(r'[\u200B-\u200D\uFEFF]', '', text)
        
        return text 