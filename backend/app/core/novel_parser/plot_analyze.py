"""
情节分析模块

负责分析小说的情节结构，包括冲突、高潮、转折点等。
"""

import re
import json
import asyncio
from typing import List, Dict, Any
from loguru import logger
from sqlalchemy.orm import Session

from ...core.database.models import NovelDocument, NovelChapter, NovelPlot


class PlotAnalyzer:
    """情节分析处理器"""
    
    def __init__(self, db: Session):
        """
        初始化处理器
        
        Args:
            db: 数据库会话
        """
        self.db = db
    
    def process(self, novel_document: NovelDocument, content: str = None) -> bool:
        """
        处理情节分析
        
        Args:
            novel_document: 小说文档对象
            content: 小说文本内容（不使用，直接从数据库获取章节）
            
        Returns:
            bool: 处理是否成功
        """
        logger.info(f"开始情节分析 - 小说ID: {novel_document.id}")
        
        try:
            # 获取所有章节
            chapters = self.db.query(NovelChapter).filter(
                NovelChapter.novel_document_id == novel_document.id
            ).order_by(NovelChapter.chapter_index).all()
            
            if not chapters:
                logger.warning(f"情节分析 - 找不到章节 - 小说ID: {novel_document.id}")
                return True  # 没有章节也算成功
            
            # 分析情节
            plot_elements = self._analyze_plot(novel_document.id, chapters)
            
            # 保存情节分析结果到数据库
            self._save_plot_analysis(novel_document.id, plot_elements)
            
            logger.info(f"情节分析完成 - 小说ID: {novel_document.id}, 共{len(plot_elements)}个情节元素")
            return True
            
        except Exception as e:
            logger.exception(f"情节分析异常 - 小说ID: {novel_document.id}, 错误: {str(e)}")
            raise
    
    def _analyze_plot(self, novel_id: int, chapters: List[NovelChapter]) -> List[Dict[str, Any]]:
        """
        分析情节结构
        
        Args:
            novel_id: 小说ID
            chapters: 章节列表
            
        Returns:
            List[Dict[str, Any]]: 情节元素列表
        """
        plot_elements = []
        
        # 计算总章节数
        total_chapters = len(chapters)
        
        # 分析故事结构（使用三幕剧结构模型）
        # 第一幕：设定和引入冲突（约占总长度的25%）
        # 第二幕：冲突发展和升级（约占总长度的50%）
        # 第三幕：冲突解决和结局（约占总长度的25%）
        
        # 第一幕
        act1_end = max(1, int(total_chapters * 0.25))
        
        # 第二幕
        act2_end = max(2, int(total_chapters * 0.75))
        
        # 第三幕
        act3_end = total_chapters
        
        # 添加三幕剧结构
        plot_elements.extend([
            {
                "plot_type": "act",
                "name": "第一幕",
                "description": "设定和引入冲突",
                "chapter_start": 1,
                "chapter_end": act1_end,
                "importance": 0.8,
                "attributes": {
                    "purpose": "introduce_setting_and_conflict"
                }
            },
            {
                "plot_type": "act",
                "name": "第二幕",
                "description": "冲突发展和升级",
                "chapter_start": act1_end + 1,
                "chapter_end": act2_end,
                "importance": 0.9,
                "attributes": {
                    "purpose": "develop_conflict"
                }
            },
            {
                "plot_type": "act",
                "name": "第三幕",
                "description": "冲突解决和结局",
                "chapter_start": act2_end + 1,
                "chapter_end": act3_end,
                "importance": 0.8,
                "attributes": {
                    "purpose": "resolve_conflict"
                }
            }
        ])
        
        # 识别关键情节点
        # 序幕：介绍主角和背景
        plot_elements.append({
            "plot_type": "plot_point",
            "name": "序幕",
            "description": "介绍主角和背景设定",
            "chapter_start": 1,
            "chapter_end": min(3, total_chapters),
            "importance": 0.7,
            "attributes": {
                "function": "introduction"
            }
        })
        
        # 引子：引发主要冲突的事件
        inciting_incident_chapter = max(1, min(int(total_chapters * 0.1), total_chapters))
        plot_elements.append({
            "plot_type": "plot_point",
            "name": "引子",
            "description": "引发主要冲突的事件",
            "chapter_start": inciting_incident_chapter,
            "chapter_end": inciting_incident_chapter + 1,
            "importance": 0.9,
            "attributes": {
                "function": "inciting_incident"
            }
        })
        
        # 第一个转折点：主角决定采取行动
        plot_point_1_chapter = max(1, min(int(total_chapters * 0.25), total_chapters))
        plot_elements.append({
            "plot_type": "plot_point",
            "name": "第一个转折点",
            "description": "主角决定采取行动",
            "chapter_start": plot_point_1_chapter,
            "chapter_end": plot_point_1_chapter + 1,
            "importance": 0.8,
            "attributes": {
                "function": "first_plot_point"
            }
        })
        
        # 中点：故事的方向发生重大转变
        midpoint_chapter = max(1, min(int(total_chapters * 0.5), total_chapters))
        plot_elements.append({
            "plot_type": "plot_point",
            "name": "中点",
            "description": "故事的方向发生重大转变",
            "chapter_start": midpoint_chapter,
            "chapter_end": midpoint_chapter + 1,
            "importance": 0.8,
            "attributes": {
                "function": "midpoint"
            }
        })
        
        # 第二个转折点：主角面临最大危机
        plot_point_2_chapter = max(1, min(int(total_chapters * 0.75), total_chapters))
        plot_elements.append({
            "plot_type": "plot_point",
            "name": "第二个转折点",
            "description": "主角面临最大危机",
            "chapter_start": plot_point_2_chapter,
            "chapter_end": plot_point_2_chapter + 1,
            "importance": 0.9,
            "attributes": {
                "function": "second_plot_point"
            }
        })
        
        # 高潮：冲突达到顶点
        climax_chapter = max(1, min(int(total_chapters * 0.9), total_chapters))
        plot_elements.append({
            "plot_type": "plot_point",
            "name": "高潮",
            "description": "冲突达到顶点",
            "chapter_start": climax_chapter,
            "chapter_end": climax_chapter + 1,
            "importance": 1.0,
            "attributes": {
                "function": "climax"
            }
        })
        
        # 结局：故事的结束
        resolution_chapter = max(1, min(total_chapters - 1, total_chapters))
        plot_elements.append({
            "plot_type": "plot_point",
            "name": "结局",
            "description": "故事的结束",
            "chapter_start": resolution_chapter,
            "chapter_end": total_chapters,
            "importance": 0.8,
            "attributes": {
                "function": "resolution"
            }
        })
        
        # 识别主要冲突
        plot_elements.append({
            "plot_type": "conflict",
            "name": "主要冲突",
            "description": "小说的核心冲突",
            "chapter_start": 1,
            "chapter_end": total_chapters,
            "importance": 1.0,
            "attributes": {
                "conflict_type": "main",
                "resolution_chapter": climax_chapter
            }
        })
        
        # 添加一些次要冲突
        subplot_count = min(3, max(1, int(total_chapters / 10)))
        for i in range(subplot_count):
            start_chapter = max(1, min(int(total_chapters * (0.2 + i * 0.2)), total_chapters))
            end_chapter = max(start_chapter + 1, min(int(start_chapter + total_chapters * 0.3), total_chapters))
            
            plot_elements.append({
                "plot_type": "conflict",
                "name": f"次要冲突 {i+1}",
                "description": f"小说的次要冲突 {i+1}",
                "chapter_start": start_chapter,
                "chapter_end": end_chapter,
                "importance": 0.6 - i * 0.1,
                "attributes": {
                    "conflict_type": "subplot",
                    "resolution_chapter": end_chapter
                }
            })
        
        return plot_elements
    
    def _save_plot_analysis(self, novel_id: int, plot_elements: List[Dict[str, Any]]) -> None:
        """
        保存情节分析结果到数据库
        
        Args:
            novel_id: 小说ID
            plot_elements: 情节元素列表
        """
        # 先删除已有的情节分析
        self.db.query(NovelPlot).filter(NovelPlot.novel_document_id == novel_id).delete()
        
        # 添加新的情节分析
        for element in plot_elements:
            plot_element = NovelPlot(
                novel_document_id=novel_id,
                plot_type=element["plot_type"],
                name=element["name"],
                description=element["description"],
                chapter_start=element["chapter_start"],
                chapter_end=element["chapter_end"],
                importance=element["importance"],
                attributes=element["attributes"]
            )
            self.db.add(plot_element)
        
        # 提交事务
        self.db.commit() 