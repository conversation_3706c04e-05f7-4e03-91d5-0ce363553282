from enum import Enum

# 解析步骤枚举
class ParseStep(str, Enum):
    NOT_STARTED = "not_started"
    CHAPTER_SPLIT = "chapter_split"
    CONTENT_CLEAN = "content_clean"
    ENTITY_EXTRACT = "entity_extract"
    RELATIONSHIP_ANALYZE = "relationship_analyze"
    TIMELINE_BUILD = "timeline_build"
    PLOT_ANALYZE = "plot_analyze"
    COMPLETED = "completed"

# 解析状态枚举
class ParseStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed" 