"""
实体提取模块

负责从小说文本中提取人物、地点、时间等实体，并保存到数据库中。
"""

import re
import json
import asyncio
from typing import List, Dict, Any
from collections import Counter
from loguru import logger
from sqlalchemy.orm import Session

from ...core.database.models import NovelDocument, NovelChapter, NovelEntity


class EntityExtractor:
    """实体提取处理器"""
    
    def __init__(self, db: Session):
        """
        初始化处理器
        
        Args:
            db: 数据库会话
        """
        self.db = db
        
        # 实体类型
        self.entity_types = ["character", "location", "time", "event", "item"]
        
        # 实体类型的中文名称
        self.entity_type_names = {
            "character": "人物",
            "location": "地点",
            "time": "时间",
            "event": "事件",
            "item": "物品"
        }
    
    def process(self, novel_document: NovelDocument, content: str = None) -> bool:
        """
        处理实体提取
        
        Args:
            novel_document: 小说文档对象
            content: 小说文本内容（不使用，直接从数据库获取章节）
            
        Returns:
            bool: 处理是否成功
        """
        logger.info(f"开始实体提取 - 小说ID: {novel_document.id}")
        
        try:
            # 获取所有章节
            chapters = self.db.query(NovelChapter).filter(
                NovelChapter.novel_document_id == novel_document.id
            ).order_by(NovelChapter.chapter_index).all()
            
            if not chapters:
                logger.warning(f"实体提取 - 找不到章节 - 小说ID: {novel_document.id}")
                return True  # 没有章节也算成功
            
            # 提取实体
            entities = self._extract_entities(novel_document.id, chapters)
            
            # 保存实体到数据库
            self._save_entities(novel_document.id, entities)
            
            logger.info(f"实体提取完成 - 小说ID: {novel_document.id}, 共{len(entities)}个实体")
            return True
            
        except Exception as e:
            logger.exception(f"实体提取异常 - 小说ID: {novel_document.id}, 错误: {str(e)}")
            raise
    
    def _extract_entities(self, novel_id: int, chapters: List[NovelChapter]) -> List[Dict[str, Any]]:
        """
        从章节中提取实体
        
        Args:
            novel_id: 小说ID
            chapters: 章节列表
            
        Returns:
            List[Dict[str, Any]]: 实体列表
        """
        # 合并所有章节内容
        full_text = "\n".join([chapter.content for chapter in chapters])
        
        # 这里应该使用NLP模型进行实体识别
        # 以下是模拟实现
        
        # 模拟人物实体
        character_entities = self._extract_characters(full_text, chapters)
        
        # 模拟地点实体
        location_entities = self._extract_locations(full_text, chapters)
        
        # 模拟时间实体
        time_entities = self._extract_times(full_text, chapters)
        
        # 模拟事件实体
        event_entities = self._extract_events(full_text, chapters)
        
        # 模拟物品实体
        item_entities = self._extract_items(full_text, chapters)
        
        # 合并所有实体
        entities = character_entities + location_entities + time_entities + event_entities + item_entities
        
        return entities
    
    def _extract_characters(self, text: str, chapters: List[NovelChapter]) -> List[Dict[str, Any]]:
        """
        提取人物实体
        
        Args:
            text: 完整文本
            chapters: 章节列表
            
        Returns:
            List[Dict[str, Any]]: 人物实体列表
        """
        # 模拟人物提取
        # 实际应用中应该使用NER模型识别人物名称
        
        # 模拟一些人物名称
        character_names = ["主角", "配角", "反派", "师父", "朋友", "敌人"]
        
        entities = []
        for i, name in enumerate(character_names):
            # 模拟提及次数和首次出现章节
            mentions = (10 - i) * 10  # 主角提及最多
            first_appearance = 1  # 假设都在第一章出现
            
            # 模拟属性
            attributes = {
                "description": f"{name}的描述",
                "gender": "male" if i % 2 == 0 else "female",
                "age": 20 + i * 5,
                "role": "protagonist" if i == 0 else "supporting" if i < 3 else "antagonist"
            }
            
            entities.append({
                "entity_type": "character",
                "name": name,
                "mentions": mentions,
                "first_appearance": first_appearance,
                "attributes": attributes
            })
        
        return entities
    
    def _extract_locations(self, text: str, chapters: List[NovelChapter]) -> List[Dict[str, Any]]:
        """
        提取地点实体
        
        Args:
            text: 完整文本
            chapters: 章节列表
            
        Returns:
            List[Dict[str, Any]]: 地点实体列表
        """
        # 模拟地点提取
        location_names = ["城市", "山脉", "河流", "森林", "村庄"]
        
        entities = []
        for i, name in enumerate(location_names):
            mentions = (5 - i) * 8
            first_appearance = i + 1
            
            attributes = {
                "description": f"{name}的描述",
                "importance": "high" if i < 2 else "medium" if i < 4 else "low"
            }
            
            entities.append({
                "entity_type": "location",
                "name": name,
                "mentions": mentions,
                "first_appearance": first_appearance,
                "attributes": attributes
            })
        
        return entities
    
    def _extract_times(self, text: str, chapters: List[NovelChapter]) -> List[Dict[str, Any]]:
        """
        提取时间实体
        
        Args:
            text: 完整文本
            chapters: 章节列表
            
        Returns:
            List[Dict[str, Any]]: 时间实体列表
        """
        # 模拟时间提取
        time_names = ["春天", "夏天", "秋天", "冬天", "黎明", "黄昏"]
        
        entities = []
        for i, name in enumerate(time_names):
            mentions = (6 - i) * 5
            first_appearance = i + 2
            
            attributes = {
                "description": f"{name}的描述",
                "duration": "season" if i < 4 else "time_of_day"
            }
            
            entities.append({
                "entity_type": "time",
                "name": name,
                "mentions": mentions,
                "first_appearance": first_appearance,
                "attributes": attributes
            })
        
        return entities
    
    def _extract_events(self, text: str, chapters: List[NovelChapter]) -> List[Dict[str, Any]]:
        """
        提取事件实体
        
        Args:
            text: 完整文本
            chapters: 章节列表
            
        Returns:
            List[Dict[str, Any]]: 事件实体列表
        """
        # 模拟事件提取
        event_names = ["战争", "婚礼", "旅行", "比赛", "庆典"]
        
        entities = []
        for i, name in enumerate(event_names):
            mentions = (5 - i) * 7
            first_appearance = i + 3
            
            attributes = {
                "description": f"{name}的描述",
                "importance": "major" if i < 2 else "minor"
            }
            
            entities.append({
                "entity_type": "event",
                "name": name,
                "mentions": mentions,
                "first_appearance": first_appearance,
                "attributes": attributes
            })
        
        return entities
    
    def _extract_items(self, text: str, chapters: List[NovelChapter]) -> List[Dict[str, Any]]:
        """
        提取物品实体
        
        Args:
            text: 完整文本
            chapters: 章节列表
            
        Returns:
            List[Dict[str, Any]]: 物品实体列表
        """
        # 模拟物品提取
        item_names = ["宝剑", "宝物", "药水", "书籍", "法器"]
        
        entities = []
        for i, name in enumerate(item_names):
            mentions = (5 - i) * 6
            first_appearance = i + 2
            
            attributes = {
                "description": f"{name}的描述",
                "importance": "key" if i < 2 else "normal"
            }
            
            entities.append({
                "entity_type": "item",
                "name": name,
                "mentions": mentions,
                "first_appearance": first_appearance,
                "attributes": attributes
            })
        
        return entities
    
    def _save_entities(self, novel_id: int, entities: List[Dict[str, Any]]) -> None:
        """
        保存实体到数据库
        
        Args:
            novel_id: 小说ID
            entities: 实体列表
        """
        # 先删除已有的实体
        self.db.query(NovelEntity).filter(NovelEntity.novel_document_id == novel_id).delete()
        
        # 添加新实体
        for entity in entities:
            novel_entity = NovelEntity(
                novel_document_id=novel_id,
                entity_type=entity["entity_type"],
                name=entity["name"],
                mentions=entity["mentions"],
                first_appearance=entity["first_appearance"],
                attributes=entity["attributes"]
            )
            self.db.add(novel_entity)
        
        # 提交事务
        self.db.commit() 