"""
时间线构建模块

负责从小说文本中提取时间信息，构建故事的时间线。
"""

import re
import asyncio
from typing import List, Dict, Any
from datetime import datetime
from loguru import logger
from sqlalchemy.orm import Session

from ...core.database.models import NovelDocument, NovelChapter, NovelEntity, NovelTimeline


class TimelineBuilder:
    """时间线构建处理器"""
    
    def __init__(self, db: Session):
        """
        初始化处理器
        
        Args:
            db: 数据库会话
        """
        self.db = db
    
    def process(self, novel_document: NovelDocument, content: str = None) -> bool:
        """
        处理时间线构建
        
        Args:
            novel_document: 小说文档对象
            content: 小说文本内容（不使用，直接从数据库获取章节和实体）
            
        Returns:
            bool: 处理是否成功
        """
        logger.info(f"开始时间线构建 - 小说ID: {novel_document.id}")
        
        try:
            # 获取所有章节
            chapters = self.db.query(NovelChapter).filter(
                NovelChapter.novel_document_id == novel_document.id
            ).order_by(NovelChapter.chapter_index).all()
            
            if not chapters:
                logger.warning(f"时间线构建 - 找不到章节 - 小说ID: {novel_document.id}")
                return True  # 没有章节也算成功
            
            # 获取时间相关的实体
            time_entities = self.db.query(NovelEntity).filter(
                NovelEntity.novel_document_id == novel_document.id,
                NovelEntity.entity_type == "time"
            ).all()
            
            # 获取事件相关的实体
            event_entities = self.db.query(NovelEntity).filter(
                NovelEntity.novel_document_id == novel_document.id,
                NovelEntity.entity_type == "event"
            ).all()
            
            # 构建时间线
            timeline_events = self._build_timeline(
                novel_document.id, 
                chapters, 
                time_entities, 
                event_entities
            )
            
            # 保存时间线到数据库
            self._save_timeline(novel_document.id, timeline_events)
            
            logger.info(f"时间线构建完成 - 小说ID: {novel_document.id}, 共{len(timeline_events)}个时间点")
            return True
            
        except Exception as e:
            logger.exception(f"时间线构建异常 - 小说ID: {novel_document.id}, 错误: {str(e)}")
            raise
    
    def _build_timeline(
        self, 
        novel_id: int, 
        chapters: List[NovelChapter],
        time_entities: List[NovelEntity],
        event_entities: List[NovelEntity]
    ) -> List[Dict[str, Any]]:
        """
        构建时间线
        
        Args:
            novel_id: 小说ID
            chapters: 章节列表
            time_entities: 时间实体列表
            event_entities: 事件实体列表
            
        Returns:
            List[Dict[str, Any]]: 时间线事件列表
        """
        timeline_events = []
        
        # 根据章节顺序创建基本时间线
        for i, chapter in enumerate(chapters):
            # 为每个章节创建一个时间点
            timeline_events.append({
                "sequence": i + 1,
                "chapter_index": chapter.chapter_index,
                "title": f"章节 {chapter.chapter_index}: {chapter.title}",
                "description": self._get_chapter_summary(chapter.content),
                "timestamp": None,  # 小说中的具体时间通常是不确定的
                "event_type": "chapter",
                "related_entities": []
            })
        
        # 基于时间实体添加时间点
        for time_entity in time_entities:
            # 获取时间实体首次出现的章节
            chapter_index = time_entity.first_appearance
            
            # 确定序列号（在章节之间插入）
            sequence = chapter_index * 10 - 5
            
            # 创建时间点
            timeline_events.append({
                "sequence": sequence,
                "chapter_index": chapter_index,
                "title": f"时间: {time_entity.name}",
                "description": time_entity.attributes.get("description", ""),
                "timestamp": None,
                "event_type": "time",
                "related_entities": [time_entity.id]
            })
        
        # 基于事件实体添加时间点
        for event_entity in event_entities:
            # 获取事件实体首次出现的章节
            chapter_index = event_entity.first_appearance
            
            # 确定序列号（在章节之间插入）
            sequence = chapter_index * 10 - 3
            
            # 创建时间点
            timeline_events.append({
                "sequence": sequence,
                "chapter_index": chapter_index,
                "title": f"事件: {event_entity.name}",
                "description": event_entity.attributes.get("description", ""),
                "timestamp": None,
                "event_type": "event",
                "related_entities": [event_entity.id]
            })
        
        # 按序列号排序
        timeline_events.sort(key=lambda x: x["sequence"])
        
        # 重新分配序列号
        for i, event in enumerate(timeline_events):
            event["sequence"] = i + 1
        
        return timeline_events
    
    def _get_chapter_summary(self, content: str) -> str:
        """
        获取章节摘要
        
        Args:
            content: 章节内容
            
        Returns:
            str: 章节摘要
        """
        # 实际应用中应该使用NLP模型生成摘要
        # 这里简单地截取前100个字符
        if len(content) > 100:
            return content[:100] + "..."
        return content
    
    def _save_timeline(self, novel_id: int, timeline_events: List[Dict[str, Any]]) -> None:
        """
        保存时间线到数据库
        
        Args:
            novel_id: 小说ID
            timeline_events: 时间线事件列表
        """
        # 先删除已有的时间线
        self.db.query(NovelTimeline).filter(NovelTimeline.novel_document_id == novel_id).delete()
        
        # 添加新时间线
        for event in timeline_events:
            timeline_event = NovelTimeline(
                novel_document_id=novel_id,
                sequence=event["sequence"],
                chapter_index=event["chapter_index"],
                title=event["title"],
                description=event["description"],
                timestamp=event["timestamp"],
                event_type=event["event_type"],
                related_entities=event["related_entities"]
            )
            self.db.add(timeline_event)
        
        # 提交事务
        self.db.commit() 