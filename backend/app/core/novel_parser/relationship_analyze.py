"""
关系分析模块

负责分析小说中实体之间的关系，如人物之间的关系、人物与地点的关系等。
"""

import asyncio
from typing import List, Dict, Any, Tuple
from loguru import logger
from sqlalchemy.orm import Session

from ...core.database.models import NovelDocument, NovelEntity, NovelRelationship


class RelationshipAnalyzer:
    """关系分析处理器"""
    
    def __init__(self, db: Session):
        """
        初始化处理器
        
        Args:
            db: 数据库会话
        """
        self.db = db
    
    def process(self, novel_document: NovelDocument, content: str = None) -> bool:
        """
        处理关系分析
        
        Args:
            novel_document: 小说文档对象
            content: 小说文本内容（不使用，直接从数据库获取实体）
            
        Returns:
            bool: 处理是否成功
        """
        logger.info(f"开始关系分析 - 小说ID: {novel_document.id}")
        
        try:
            # 获取所有实体
            entities = self.db.query(NovelEntity).filter(
                NovelEntity.novel_document_id == novel_document.id
            ).all()
            
            if not entities:
                logger.warning(f"关系分析 - 找不到实体 - 小说ID: {novel_document.id}")
                return True  # 没有实体也算成功
            
            # 分析实体之间的关系
            relationships = self._analyze_relationships(novel_document.id, entities)
            
            # 保存关系到数据库
            self._save_relationships(novel_document.id, relationships)
            
            logger.info(f"关系分析完成 - 小说ID: {novel_document.id}, 共{len(relationships)}个关系")
            return True
            
        except Exception as e:
            logger.exception(f"关系分析异常 - 小说ID: {novel_document.id}, 错误: {str(e)}")
            raise
    
    def _analyze_relationships(self, novel_id: int, entities: List[NovelEntity]) -> List[Dict[str, Any]]:
        """
        分析实体之间的关系
        
        Args:
            novel_id: 小说ID
            entities: 实体列表
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        # 按类型分组实体
        entity_by_type = {}
        for entity in entities:
            if entity.entity_type not in entity_by_type:
                entity_by_type[entity.entity_type] = []
            entity_by_type[entity.entity_type].append(entity)
        
        relationships = []
        
        # 分析人物之间的关系
        if "character" in entity_by_type:
            character_relationships = self._analyze_character_relationships(
                entity_by_type["character"]
            )
            relationships.extend(character_relationships)
        
        # 分析人物与地点的关系
        if "character" in entity_by_type and "location" in entity_by_type:
            character_location_relationships = self._analyze_character_location_relationships(
                entity_by_type["character"],
                entity_by_type["location"]
            )
            relationships.extend(character_location_relationships)
        
        # 分析人物与事件的关系
        if "character" in entity_by_type and "event" in entity_by_type:
            character_event_relationships = self._analyze_character_event_relationships(
                entity_by_type["character"],
                entity_by_type["event"]
            )
            relationships.extend(character_event_relationships)
        
        # 分析人物与物品的关系
        if "character" in entity_by_type and "item" in entity_by_type:
            character_item_relationships = self._analyze_character_item_relationships(
                entity_by_type["character"],
                entity_by_type["item"]
            )
            relationships.extend(character_item_relationships)
        
        return relationships
    
    def _analyze_character_relationships(self, characters: List[NovelEntity]) -> List[Dict[str, Any]]:
        """
        分析人物之间的关系
        
        Args:
            characters: 人物实体列表
            
        Returns:
            List[Dict[str, Any]]: 人物关系列表
        """
        relationships = []
        
        # 模拟人物关系
        # 实际应用中应该基于文本内容分析人物之间的互动、对话等
        relation_types = ["friend", "enemy", "family", "lover", "master_apprentice"]
        
        # 为每对人物生成一种关系
        for i in range(len(characters)):
            for j in range(i + 1, len(characters)):
                # 选择一种关系类型
                relation_type = relation_types[(i + j) % len(relation_types)]
                
                # 根据关系类型设置强度
                strength = 0.5 + (i + j) % 5 * 0.1
                
                # 创建关系
                relationships.append({
                    "source_id": characters[i].id,
                    "target_id": characters[j].id,
                    "relation_type": relation_type,
                    "attributes": {
                        "strength": strength,
                        "description": f"{characters[i].name}与{characters[j].name}的{relation_type}关系"
                    }
                })
        
        return relationships
    
    def _analyze_character_location_relationships(
        self, characters: List[NovelEntity], locations: List[NovelEntity]
    ) -> List[Dict[str, Any]]:
        """
        分析人物与地点的关系
        
        Args:
            characters: 人物实体列表
            locations: 地点实体列表
            
        Returns:
            List[Dict[str, Any]]: 人物与地点的关系列表
        """
        relationships = []
        
        # 模拟人物与地点的关系
        relation_types = ["lives_in", "visited", "born_in", "works_in"]
        
        # 为每个人物生成与一些地点的关系
        for i, character in enumerate(characters):
            # 为每个人物选择1-3个地点
            num_locations = min(1 + i % 3, len(locations))
            for j in range(num_locations):
                # 选择一种关系类型
                relation_type = relation_types[(i + j) % len(relation_types)]
                
                # 创建关系
                relationships.append({
                    "source_id": character.id,
                    "target_id": locations[j].id,
                    "relation_type": relation_type,
                    "attributes": {
                        "frequency": 0.3 + (i + j) % 7 * 0.1,
                        "description": f"{character.name}{relation_type}{locations[j].name}"
                    }
                })
        
        return relationships
    
    def _analyze_character_event_relationships(
        self, characters: List[NovelEntity], events: List[NovelEntity]
    ) -> List[Dict[str, Any]]:
        """
        分析人物与事件的关系
        
        Args:
            characters: 人物实体列表
            events: 事件实体列表
            
        Returns:
            List[Dict[str, Any]]: 人物与事件的关系列表
        """
        relationships = []
        
        # 模拟人物与事件的关系
        relation_types = ["participated_in", "witnessed", "organized", "affected_by"]
        
        # 为每个事件生成与一些人物的关系
        for i, event in enumerate(events):
            # 为每个事件选择2-4个人物
            num_characters = min(2 + i % 3, len(characters))
            for j in range(num_characters):
                # 选择一种关系类型
                relation_type = relation_types[(i + j) % len(relation_types)]
                
                # 创建关系
                relationships.append({
                    "source_id": characters[j].id,
                    "target_id": event.id,
                    "relation_type": relation_type,
                    "attributes": {
                        "importance": 0.4 + (i + j) % 6 * 0.1,
                        "description": f"{characters[j].name}{relation_type}{event.name}"
                    }
                })
        
        return relationships
    
    def _analyze_character_item_relationships(
        self, characters: List[NovelEntity], items: List[NovelEntity]
    ) -> List[Dict[str, Any]]:
        """
        分析人物与物品的关系
        
        Args:
            characters: 人物实体列表
            items: 物品实体列表
            
        Returns:
            List[Dict[str, Any]]: 人物与物品的关系列表
        """
        relationships = []
        
        # 模拟人物与物品的关系
        relation_types = ["owns", "uses", "created", "seeks"]
        
        # 为每个人物生成与一些物品的关系
        for i, character in enumerate(characters):
            # 为每个人物选择1-2个物品
            num_items = min(1 + i % 2, len(items))
            for j in range(num_items):
                # 选择一种关系类型
                relation_type = relation_types[(i + j) % len(relation_types)]
                
                # 创建关系
                relationships.append({
                    "source_id": character.id,
                    "target_id": items[j].id,
                    "relation_type": relation_type,
                    "attributes": {
                        "significance": 0.5 + (i + j) % 5 * 0.1,
                        "description": f"{character.name}{relation_type}{items[j].name}"
                    }
                })
        
        return relationships
    
    def _save_relationships(self, novel_id: int, relationships: List[Dict[str, Any]]) -> None:
        """
        保存关系到数据库
        
        Args:
            novel_id: 小说ID
            relationships: 关系列表
        """
        # 先删除已有的关系
        self.db.query(NovelRelationship).filter(
            NovelRelationship.source_entity.has(novel_document_id=novel_id) |
            NovelRelationship.target_entity.has(novel_document_id=novel_id)
        ).delete(synchronize_session=False)
        
        # 添加新关系
        for relationship in relationships:
            novel_relationship = NovelRelationship(
                source_entity_id=relationship["source_id"],
                target_entity_id=relationship["target_id"],
                relation_type=relationship["relation_type"],
                attributes=relationship["attributes"]
            )
            self.db.add(novel_relationship)
        
        # 提交事务
        self.db.commit() 