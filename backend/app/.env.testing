# 测试环境配置
ENV=testing
DEBUG=true
PROJECT_NAME=AI绘本与短视频创作平台
VERSION=0.1.0
API_V1_STR=/api/v1
LOG_LEVEL=INFO

# 服务器配置
HOST=0.0.0.0
PORT=8080
WORKERS=4

# 数据库配置
DATABASE_URL=postgresql://tinytales_user:13569813617Ch@localhost:5432/tinytales_test
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10

# Redis配置
REDIS_URL=redis://localhost:6379/1
REDIS_POOL_SIZE=10

# JWT配置
SECRET_KEY=d97f4d5fa482bd897aa980fa65992fa867ff73d61b9466f45083170c5773abe9
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI服务配置
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_API_BASE=https://api.openai.com/v1
QIANWEN_API_KEY=sk-45d77088274048be8ad0ee334ac3cae0
QIANWEN_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
DEFAULT_AI_SERVICE=qianwen
DEFAULT_OPENAI_MODEL=gpt-4-turbo-preview
DEFAULT_QIANWEN_MODEL=qwen-turbo

# 安全配置
ALLOWED_HOSTS=["*"]
CORS_ORIGINS=["*"]
RATE_LIMIT=0

# 测试配置
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=test_password123

# AES加密配置
AES_KEY=HVJVXfixSsto2xndnpp8/FtFUPAdvGPIoDkryG3TuRI=
AES_IV=aiil7bqQ1cALMKIHpautFg==
AES_ENCRYPTION_VERSION=1
AES_ENCRYPTION_ENABLED=true

# 数据库配置
INIT_DB=true  # 生产环境不自动初始化数据库