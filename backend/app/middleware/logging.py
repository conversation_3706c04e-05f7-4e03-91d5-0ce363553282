# Python 标准库导入
import json
import time
from typing import Callable

# FastAPI 相关导入
from fastapi import FastAPI, Request, Response
from fastapi.responses import J<PERSON><PERSON>esponse, FileResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import StreamingResponse
from loguru import logger

# 导入自定义JSON工具
from ..core.utils.json import json_dumps
from ..schemas.upload import UserUploadListResponse

class LoggingMiddleware(BaseHTTPMiddleware):
    """日志中间件，记录请求和响应信息"""
    async def dispatch(self, request: Request, call_next):
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取请求信息
        request_body = None
        body_log = None
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                request_body = await request.json()
                body_log = json_dumps(request_body)
            except:
                request_body = await request.body()
                # 如果是二进制数据，只记录其长度和类型，不尝试序列化
                if isinstance(request_body, bytes):
                    content_type = request.headers.get("content-type", "")
                    body_log = f"<binary data: {len(request_body)} bytes, content-type: {content_type}>"
                else:
                    body_log = str(request_body)
        
        # 记录详细的请求日志
        logger.info(
            "请求信息:\n方法: {} {}\n请求头: {}\n查询参数: {}\n请求体: {}",
            request.method,
            request.url.path,
            json_dumps(dict(request.headers)),
            json_dumps(dict(request.query_params)),
            body_log
        )
        
        # 处理请求
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 准备响应日志
        response_info = {
            "type": type(response).__name__,
            "status_code": response.status_code,
            "headers": dict(response.headers),
            "process_time": f"{process_time:.3f}s"
        }

        # 根据响应类型添加额外信息
        # 检查是否是流式响应或文件响应（包括内部类型 _StreamingResponse）
        is_streaming = isinstance(response, StreamingResponse) or "_StreamingResponse" in str(type(response))
        is_file = isinstance(response, FileResponse)
        isinstance_response = is_streaming or is_file
        
        logger.info(f"响应类型检查: is_streaming={is_streaming}, is_file={is_file}")
        logger.info(f"响应对象类型: {type(response)}")
        
        response_content = None
        
        # 获取内容类型
        content_type = response.headers.get("content-type", "")
        is_json_response = "application/json" in content_type
        
        # 处理不同类型的响应
        if is_json_response:
            logger.info("检测到 JSON 类型响应")
            
            # 尝试获取响应内容
            if isinstance_response and hasattr(response, "body_iterator"):
                try:
                    # 获取流式响应内容
                    logger.info("读取流式 JSON 响应内容")
                    response_body = [chunk async for chunk in response.body_iterator]
                    content = b''.join(response_body).decode('utf-8')
                    
                    # 重新设置响应的 body_iterator
                    async def new_iterator():
                        yield content.encode('utf-8')
                    response.body_iterator = new_iterator()
                    
                    # 解析 JSON 内容
                    try:
                        response_content = json.loads(content)
                        logger.info(f"成功解析流式 JSON 响应，长度: {len(content)}")
                    except Exception as e:
                        logger.error(f"解析流式 JSON 失败: {str(e)}")
                        response_content = content
                except Exception as e:
                    error_msg = f"读取流式响应内容失败: {str(e)}"
                    logger.error(error_msg)
                    response_content = {"error": error_msg}
            elif hasattr(response, "body"):
                try:
                    # 获取普通响应内容
                    logger.info("读取普通 JSON 响应内容")
                    content = response.body.decode('utf-8')
                    try:
                        response_content = json.loads(content)
                        logger.info(f"成功解析 JSON 响应，长度: {len(content)}")
                    except Exception as e:
                        logger.error(f"解析 JSON 失败: {str(e)}")
                        response_content = content
                except Exception as e:
                    error_msg = f"读取响应内容失败: {str(e)}"
                    logger.error(error_msg)
                    response_content = {"error": error_msg}
            else:
                logger.warning(f"无法读取 JSON 响应内容，响应对象: {type(response).__name__}")
                response_content = {"error": f"无法读取响应内容，类型: {type(response).__name__}"}
        elif is_file:
            # 文件响应
            filename = getattr(response, "filename", "未知文件")
            media_type = getattr(response, "media_type", "未知")
            response_content = {"type": "file_response", "filename": filename, "media_type": media_type}
        elif is_streaming:
            # 非JSON的流式响应
            media_type = getattr(response, "media_type", "未知")
            response_content = {"type": "streaming_response", "media_type": media_type}
        elif any(binary_type in content_type for binary_type in ["image/", "video/", "audio/", "application/octet-stream"]):
            # 二进制响应
            response_content = {"type": "binary_response", "content_type": content_type}
        else:
            # 其他类型响应
            try:
                if hasattr(response, "body"):
                    content = response.body.decode('utf-8')
                    response_content = content
                else:
                    logger.warning(f"响应对象 {type(response).__name__} 没有 body 属性")
                    response_content = {"error": f"{type(response).__name__} without body attribute"}
            except Exception as e:
                error_msg = f"内容解码失败: {str(e)}"
                logger.error(error_msg)
                response_content = {"error": error_msg}
        
        # 记录详细的响应日志
        response_log = {
            "status_code": response.status_code,
            "process_time": f"{process_time:.3f}s",
            "headers": dict(response.headers),
            "content": response_content
        }
        
        logger.info(
            "响应信息:\n{}",
            json_dumps(response_log, indent=2)  # 使用缩进格式化输出
        )
        logger.info(f"Logging Middleware - Total processing time: {process_time:.3f} seconds for {request.method} {request.url.path}")
        
        # 如果是流式响应，我们需要确保它被正确处理
        if is_streaming:
            # 检查是否有 body_iterator 属性
            if hasattr(response, "body_iterator"):
                # 创建一个新的StreamingResponse，保持原始响应的所有属性
                return StreamingResponse(
                    response.body_iterator,
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    media_type=getattr(response, "media_type", None)
                )
            else:
                logger.warning(f"无法重新包装流式响应，缺少 body_iterator 属性: {type(response)}")
        
        return response