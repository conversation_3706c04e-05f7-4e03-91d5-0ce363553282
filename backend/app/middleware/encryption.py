# Python 标准库导入
import json
from typing import Callable, List, Optional

# FastAPI 相关导入
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

# 本地模块导入
from ..core.utils.encryption import Encryption
from loguru import logger
import os

class EncryptionMiddleware(BaseHTTPMiddleware):
    def __init__(
        self,
        app: ASGIApp,
        encryption: Encryption,
        exclude_paths: Optional[List[str]] = None
    ):
        super().__init__(app)
        self.encryption = encryption
        self.exclude_paths = exclude_paths or []

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        import time
        start_time = time.time()
        # 记录请求开始
        logger.info(f"Encryption Middleware - Processing request: {request.method} {request.url.path}")
        
        # 检查是否在排除路径中
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            logger.info(f"Encryption Middleware - Path excluded: {request.url.path}")
            return await call_next(request)

        # 处理请求体加密
        if request.method in ["POST", "PUT", "DELETE"]:
            try:
                body = await request.body()
                if body:
                    logger.info(f"Encryption Middleware - Request body: {body.decode()}")
                    # 获取加密后的数据
                    encrypted_data = json.loads(body)
                    if "data" in encrypted_data:
                        logger.info("Encryption Middleware - Decrypting request data")
                        # 解密数据
                        decrypted_data = self.encryption.decrypt(encrypted_data["data"])
                        # 替换请求体
                        request._body = json.dumps(decrypted_data).encode()
                        logger.info(f"Encryption Middleware - Decrypted data: {decrypted_data}")
                    else:
                        logger.info("Encryption Middleware - No data field found in request")
                        return JSONResponse(
                            content={
                                "code": 400,
                                "message": "请求参数必须包含加密的data字段",
                                "data": None
                            },
                            status_code=400
                        )
            except Exception as e:
                logger.error(f"Encryption Middleware - Request decryption failed: {str(e)}")
                return JSONResponse(
                    content={
                        "code": 400,
                        "message": f"请求数据解密失败: {str(e)}",
                        "data": None
                    },
                    status_code=400
                )

        # 处理查询参数加密
        if request.method == "GET":
            # 检查是否包含加密参数q
            if not request.query_params:
                # 如果没有任何参数，直接放行
                return await call_next(request)
            if "q" not in request.query_params:
                logger.info("Encryption Middleware - No encrypted query parameter 'q' found in GET request")
                return JSONResponse(
                    content={
                        "code": 400,
                        "message": "GET请求必须包含加密的q参数",
                        "data": None
                    },
                    status_code=400
                )
            
            try:
                logger.info("Encryption Middleware - Decrypting query parameters")
                # 解密查询参数
                encrypted_query = request.query_params["q"]
                decrypted_query = self.encryption.decrypt(encrypted_query)
                # 更新查询参数
                if isinstance(decrypted_query, dict):
                    request.state.decrypted_query = decrypted_query
                    logger.info(f"Encryption Middleware - Decrypted query params: {decrypted_query}")
                else:
                    logger.error("Encryption Middleware - Decrypted query is not a dict")
                    return JSONResponse(
                        content={
                            "code": 400,
                            "message": "解密后的查询参数必须是一个字典",
                            "data": None
                        },
                        status_code=400
                    )
            except Exception as e:
                logger.error(f"Encryption Middleware - Query decryption failed: {str(e)}")
                return JSONResponse(
                    content={
                        "code": 400,
                        "message": f"查询参数解密失败: {str(e)}",
                        "data": None
                    },
                    status_code=400
                )

        # 获取原始响应
        logger.info("Encryption Middleware - Calling next middleware/route handler")
        response = await call_next(request)
        logger.info(f"Encryption Middleware - Response status code: {response.status_code}")

        # 检查是否在排除路径中，如果是则直接返回原始响应
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            logger.info(f"Encryption Middleware - Path excluded for response: {request.url.path}")
            return response

        # 处理响应加密
        if isinstance(response, JSONResponse):
            try:
                # 获取响应内容
                content = response.body.decode('utf-8')
                content_json = json.loads(content)
                logger.info(f"Encryption Middleware - Original response content: {content_json}")

                # 检查是否已经有标准格式，如果没有则进行格式化
                if not all(key in content_json for key in ["code", "message"]):
                    # 使用标准格式封装
                    formatted_content = {
                        "code": response.status_code,
                        "message": "success",
                        "data": content_json
                    }
                else:
                    # 已经是标准格式，直接使用
                    formatted_content = content_json

                # 如果有实际数据且不在排除路径中，则进行加密
                if "data" in formatted_content and formatted_content["data"] is not None:
                    # 对数据进行加密
                    encrypted_data = self.encryption.encrypt(formatted_content["data"])
                    # 替换为加密后的数据
                    formatted_content["data"] = encrypted_data
                    logger.info("Encryption Middleware - Response data encrypted")
                
                # 复制原始headers但排除Content-Length
                headers = {}
                for name, value in response.headers.items():
                    if name.lower() != 'content-length':
                        headers[name] = value
                
                # 创建新的响应
                return JSONResponse(
                    content=formatted_content,
                    status_code=response.status_code,
                    headers=headers
                )
            except Exception as e:
                logger.error(f"Encryption Middleware - Response encryption failed: {str(e)}")
                # 如果加密失败，返回原始响应
                return response
        elif hasattr(response, 'body_iterator'):
            # 处理流式响应
            try:
                # 获取headers中的content-type
                content_type = None
                for name, value in response.headers.items():
                    if name.lower() == 'content-type':
                        content_type = value
                        break
                
                # 如果是JSON响应，尝试获取并加密内容
                if content_type and 'application/json' in content_type:
                    # 读取响应体
                    body_content = b""
                    async for chunk in response.body_iterator:
                        if isinstance(chunk, str):
                            body_content += chunk.encode('utf-8')
                        else:
                            body_content += chunk
                    
                    # 解析JSON
                    try:
                        content_json = json.loads(body_content.decode('utf-8'))
                        logger.info(f"Encryption Middleware - Streaming response content: {content_json}")
                        
                        # 检查是否已经有标准格式
                        if not all(key in content_json for key in ["code", "message"]):
                            # 使用标准格式封装
                            formatted_content = {
                                "code": response.status_code,
                                "message": "success",
                                "data": content_json
                            }
                        else:
                            # 已经是标准格式，直接使用
                            formatted_content = content_json
                        
                        # 如果有实际数据，则进行加密
                        if "data" in formatted_content and formatted_content["data"] is not None:
                            # 对数据进行加密
                            encrypted_data = self.encryption.encrypt(formatted_content["data"])
                            # 替换为加密后的数据
                            formatted_content["data"] = encrypted_data
                            logger.info("Encryption Middleware - Streaming response data encrypted")
                        
                        # 复制原始headers但排除Content-Length
                        headers = {}
                        for name, value in response.headers.items():
                            if name.lower() != 'content-length':
                                headers[name] = value
                        
                        # 创建新的JSONResponse
                        return JSONResponse(
                            content=formatted_content,
                            status_code=response.status_code,
                            headers=headers
                        )
                    except json.JSONDecodeError as e:
                        logger.error(f"Encryption Middleware - JSON decode error: {str(e)}")
                        # 如果解析失败，返回原始响应
                        return response
            except Exception as e:
                logger.error(f"Encryption Middleware - Error processing streaming response: {str(e)}")
                return response
        
        logger.info("Encryption Middleware - Request processing completed")
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"Encryption Middleware - Total processing time: {duration:.3f} seconds for {request.method} {request.url.path}")
        return response 