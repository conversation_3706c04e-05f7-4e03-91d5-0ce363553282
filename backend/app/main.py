# FastAPI 核心组件
from fastapi import Fast<PERSON>I, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
import json

# 项目配置和工具
from .core.config import settings
from .core.utils.exceptions import APIException
from .core.utils.handlers import (
    api_exception_handler,
    validation_exception_handler,
    http_exception_handler,
    general_exception_handler
)
from .core.utils.encryption import Encryption
from .core.utils.json import CustomJSONEncoder

# 中间件
from .middleware import EncryptionMiddleware, LoggingMiddleware

# API路由
from .api import api_router

# 替换默认的JSON编码器
fastapi_jsonencoder = json.JSONEncoder
json.JSONEncoder = CustomJSONEncoder

app = FastAPI(
    root_path="/flowpic",
    title=settings.PROJECT_NAME,
    description="提供AI绘本和短视频创作服务的后端API",
    version="settings.VERSION",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化加密中间件（如果启用）
if settings.AES_ENCRYPTION_ENABLED:
    encryption = Encryption(
        key=settings.AES_KEY,
        iv=settings.AES_IV
    )
    
    # 添加加密中间件
    app.add_middleware(
        EncryptionMiddleware,
        encryption=encryption,
        exclude_paths=settings.AES_EXCLUDE_PATHS
    )

# 添加日志中间件
app.add_middleware(LoggingMiddleware)

# 注册异常处理器
# 1. 处理自定义API异常（如：参数错误、权限错误、资源不存在等）
# 例如：raise APIException(code=400, message="用户名已存在")
app.add_exception_handler(APIException, api_exception_handler)

# 2. 处理FastAPI的HTTPException（如：权限验证失败、资源不存在等）
# 例如：raise HTTPException(status_code=400, detail="用户名已存在")
app.add_exception_handler(HTTPException, http_exception_handler)

# 3. 处理请求参数验证异常（如：参数类型错误、必填字段缺失、长度限制等）
# 例如：当请求数据不符合 Pydantic 模型定义时自动触发
# 例如：username长度小于5个字符，password长度小于8个字符
app.add_exception_handler(RequestValidationError, validation_exception_handler)

# 4. 处理所有其他未捕获的异常（作为最后的异常处理防线）
# 例如：数据库连接错误、第三方服务调用失败等
app.add_exception_handler(Exception, general_exception_handler)

# 注册路由
app.include_router(api_router, prefix=settings.API_V1_STR)

@app.get("/")
async def root():
    return {"message": "欢迎使用AI绘本与短视频创作平台API"} 