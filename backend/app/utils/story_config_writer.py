"""
故事配置写入工具
用于更新故事配置模型中的字段
"""

from typing import Any, Dict
from ..schemas import StoryConfigModel, CharacterModel, SceneModel

class StoryConfigWriter:
    """
    故事配置写入器
    用于更新故事配置模型中的字段
    """
    
    def __init__(self, story_config: StoryConfigModel):
        """
        初始化故事配置写入器
        
        Args:
            story_config: 故事配置模型
        """
        self.config = story_config
    
    def write(self, field_path: str, value: Any) -> bool:
        """
        写入字段值
        
        Args:
            field_path: 字段路径，例如 "story_outline.theme"
            value: 字段值
            
        Returns:
            bool: 是否成功写入
        """
        try:
            # 处理嵌套字段
            parts = field_path.split('.')
            
            # 处理主要字段
            if len(parts) == 1:
                # 直接字段，如 age_range, gender
                if hasattr(self.config, parts[0]):
                    setattr(self.config, parts[0], value)
                    return True
                return False
            
            # 处理嵌套字段
            if parts[0] == 'story_outline':
                # 故事大纲字段
                if len(parts) == 2 and hasattr(self.config.story_outline, parts[1]):
                    setattr(self.config.story_outline, parts[1], value)
                    return True
            
            elif parts[0] == 'main_character':
                # 主角字段
                if len(parts) == 2 and hasattr(self.config.main_character, parts[1]):
                    setattr(self.config.main_character, parts[1], value)
                    return True
            
            elif parts[0] == 'side_characters':
                # 配角字段，格式: side_characters.0.name
                if len(parts) == 3 and parts[1].isdigit():
                    index = int(parts[1])
                    # 确保配角列表有足够的元素
                    while len(self.config.side_characters) <= index:
                        self.config.side_characters.append(CharacterModel())
                    
                    if hasattr(self.config.side_characters[index], parts[2]):
                        setattr(self.config.side_characters[index], parts[2], value)
                        return True
            
            elif parts[0] == 'scene':
                # 场景字段，格式: scene.0.place
                if len(parts) == 3 and parts[1].isdigit():
                    index = int(parts[1])
                    # 确保场景列表有足够的元素
                    while len(self.config.scene) <= index:
                        self.config.scene.append(SceneModel())
                    
                    if hasattr(self.config.scene[index], parts[2]):
                        setattr(self.config.scene[index], parts[2], value)
                        return True
            
            return False
        
        except Exception as e:
            print(f"写入字段 {field_path} 失败: {e}")
            return False 