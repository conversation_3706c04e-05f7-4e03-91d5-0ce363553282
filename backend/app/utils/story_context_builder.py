"""
故事上下文构建工具
用于将故事配置转换为文本上下文
"""

from typing import List, Dict, Any
from ..schemas import StoryConfigModel, CharacterModel, SceneModel

class StoryContextBuilder:
    """
    故事上下文构建器
    将故事配置模型转换为文本上下文
    """
    
    def __init__(self, story_config: StoryConfigModel):
        """
        初始化故事上下文构建器
        
        Args:
            story_config: 故事配置模型
        """
        self.config = story_config
    
    def to_text(self) -> str:
        """
        将故事配置转换为文本上下文
        
        Returns:
            str: 文本上下文
        """
        context_parts = []
        
        # 基本信息
        if self.config.age_range:
            context_parts.append(f"目标年龄段: {self.config.age_range}")
        if self.config.gender:
            context_parts.append(f"主角性别倾向: {self.config.gender}")
        
        # 故事大纲
        outline = self.config.story_outline
        if any([outline.theme, outline.starting_point, outline.trigger_event, 
                outline.emotional_reaction, outline.growth_process, outline.climax, 
                outline.resolution, outline.lesson]):
            context_parts.append("\n【故事大纲】")
            if outline.theme:
                context_parts.append(f"主题: {outline.theme}")
            if outline.starting_point:
                context_parts.append(f"开端: {outline.starting_point}")
            if outline.trigger_event:
                context_parts.append(f"触发事件: {outline.trigger_event}")
            if outline.emotional_reaction:
                context_parts.append(f"情绪反应: {outline.emotional_reaction}")
            if outline.growth_process:
                context_parts.append(f"成长过程: {outline.growth_process}")
            if outline.climax:
                context_parts.append(f"故事高潮: {outline.climax}")
            if outline.resolution:
                context_parts.append(f"结局: {outline.resolution}")
            if outline.lesson:
                context_parts.append(f"教育意义: {outline.lesson}")
        
        # 主角
        main_char = self.config.main_character
        if any([main_char.name, main_char.type, main_char.trait, main_char.age, main_char.description]):
            context_parts.append("\n【主角】")
            if main_char.name:
                context_parts.append(f"名字: {main_char.name}")
            if main_char.type:
                context_parts.append(f"类型: {main_char.type}")
            if main_char.trait:
                context_parts.append(f"特点: {main_char.trait}")
            if main_char.age:
                context_parts.append(f"年龄: {main_char.age}")
            if main_char.description:
                context_parts.append(f"描述: {main_char.description}")
        
        # 配角
        if self.config.side_characters:
            context_parts.append("\n【配角】")
            for i, char in enumerate(self.config.side_characters, 1):
                if any([char.name, char.type, char.trait, char.age, char.description]):
                    context_parts.append(f"配角 {i}:")
                    if char.name:
                        context_parts.append(f"  名字: {char.name}")
                    if char.type:
                        context_parts.append(f"  类型: {char.type}")
                    if char.trait:
                        context_parts.append(f"  特点: {char.trait}")
                    if char.age:
                        context_parts.append(f"  年龄: {char.age}")
                    if char.description:
                        context_parts.append(f"  描述: {char.description}")
        
        # 场景
        if self.config.scene:
            context_parts.append("\n【场景】")
            for i, scene in enumerate(self.config.scene, 1):
                if any([scene.place, scene.time, scene.description]):
                    context_parts.append(f"场景 {i}:")
                    if scene.place:
                        context_parts.append(f"  地点: {scene.place}")
                    if scene.time:
                        context_parts.append(f"  时间: {scene.time}")
                    if scene.description:
                        context_parts.append(f"  描述: {scene.description}")
        
        return "\n".join(context_parts) 