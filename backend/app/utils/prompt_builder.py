"""
提示词构建工具
用于构建发送给AI模型的提示词
"""

def build_prompt(user_message: str, context: str = "", directive_prompt: str = "") -> str:
    """
    构建发送给AI模型的提示词
    
    Args:
        user_message: 用户消息
        context: 故事上下文
        directive_prompt: 指令提示词
        
    Returns:
        str: 完整的提示词
    """
    # 基础系统提示词
    system_prompt = """你是一个专业的儿童故事创作助手，擅长创作3-8岁儿童的故事。
请根据用户的需求，创作适合儿童阅读的故事。
故事应该具有教育意义，语言简单易懂，富有想象力和趣味性。

如果用户提供了故事配置信息，请根据配置信息进行创作。
如果用户提供了特定指令，请按照指令进行操作。

在回复中，请使用友好、温暖的语气，避免使用复杂的词汇和句式。
"""

    # 构建完整提示词
    prompt_parts = [system_prompt]
    
    # 添加上下文（如果有）
    if context:
        prompt_parts.append("\n【当前故事配置】\n" + context)
    
    # 添加指令提示词（如果有）
    if directive_prompt:
        prompt_parts.append("\n【特殊指令】\n" + directive_prompt)
    
    # 添加用户消息
    prompt_parts.append("\n【用户消息】\n" + user_message)
    
    # 添加输出格式指导
    prompt_parts.append("""
【输出格式】
如果你要更新故事配置中的字段，请在回复中使用以下JSON格式：

```json
{
  "字段名": "字段值",
  "另一个字段": "另一个值"
}
```

例如，如果你要更新故事主题和主角名称，可以这样回复：

```json
{
  "story_outline.theme": "友谊的力量",
  "main_character.name": "小兔子乐乐"
}
```
""")
    
    return "\n".join(prompt_parts) 