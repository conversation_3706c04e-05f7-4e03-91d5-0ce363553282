# FastAPI 相关导入
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OA<PERSON>2<PERSON><PERSON>word<PERSON>earer, OAuth2PasswordRequestForm
from fastapi.responses import JSONResponse
from loguru import logger
import sys
from pathlib import Path

# 配置loguru
logger.remove()  # 移除默认的处理器
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)
logger.add(
    "logs/api.log",
    rotation="10 MB",
    retention="1 week",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="INFO",
    encoding="utf-8"
)

# 数据库相关导入
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

# 本地模块导入
from ...core.database import get_db
from ...core.utils.security import (
    verify_password,
    get_password_hash,
    create_access_token,
    verify_token,
    get_current_user
)
from ...core.config import settings
from ...core.database.models.user import User
from ...core.database.models.token import Token as TokenModel
from ...schemas.user import (
    UserCreate,
    UserResponse,
    UserUpdate,

)
from ...schemas.auth import (
    UserLogin,
    Token,
    UserWithTokens,
    PasswordChange
)
from ...schemas.response import ResponseModel
from ...core.utils.json import json_dumps  # 导入自定义的JSON序列化函数

router = APIRouter()

@router.post("/register", response_model=ResponseModel)
async def register(request: Request, user: UserCreate, db: Session = Depends(get_db)):
    """用户注册"""
    logger.info("开始处理注册请求 - 用户名: {}", user.username)
    logger.info(f"注册参数内容: {user.dict()}")
    
    # 检查用户名是否已存在
    db_user = db.query(User).filter(User.username == user.username).first()
    if db_user:
        logger.warning("注册失败 - 用户名已存在: {}", user.username)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    db_user = db.query(User).filter(User.email == user.email).first()
    if db_user:
        logger.warning("注册失败 - 邮箱已注册: {}", user.email)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已注册"
        )
    
    hashed_password = get_password_hash(user.password)
    db_user = User(
        username=user.username,
        email=user.email,
        nickname=user.nickname,
        hashed_password=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    logger.info("注册成功 - 用户ID: {}", db_user.id)
    
    # 创建响应内容
    response_data = {
        "code": 200,
        "message": "注册成功",
        "data": {
            "user_id": str(db_user.id),
            "username": db_user.username,
            "email": db_user.email,
            "nickname": db_user.nickname or "",  # 如果为None则返回空字符串
            "created_at": db_user.created_at
        }
    }
    
    # 使用自定义JSON编码器创建响应
    return JSONResponse(
        content=response_data,
        media_type="application/json",
        # 确保datetime可以被正确序列化
        headers={"Content-Type": "application/json; charset=utf-8"}
    )

@router.post("/login", response_model=ResponseModel)
async def login(user_data: UserLogin, db: Session = Depends(get_db)):
    """用户登录，返回token和refresh_token，支持用户名或邮箱登录"""
    login_id = user_data.username
    logger.info(f"登录请求 - 登录ID: {login_id}")
    
    # 查询用户 - 支持用户名或邮箱登录
    user = None
    # 尝试通过用户名查找
    user = db.query(User).filter(User.username == login_id).first()
    
    # 如果未找到用户，尝试通过邮箱查找
    if not user:
        user = db.query(User).filter(User.email == login_id).first()
    
    # 验证用户和密码
    if not user or not verify_password(user_data.password, user.hashed_password):
        logger.warning(f"登录失败 - 用户名/邮箱或密码错误: {login_id}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="用户名/邮箱或密码错误")
    
    # 将token过期时间设置为24小时
    expires_delta = timedelta(hours=10000)
    expires_at = datetime.utcnow() + expires_delta
    access_token = create_access_token(data={"sub": user.username}, expires_delta=expires_delta)
    refresh_token = create_access_token(data={"sub": user.username, "type": "refresh"}, expires_delta=timedelta(days=7))
    
    # 将该用户之前的token标记为非活跃
    db.query(TokenModel).filter(
        TokenModel.user_id == user.id, 
        TokenModel.is_active == True
    ).update({"is_active": False})
    
    # 创建新token
    token = TokenModel(user_id=user.id, access_token=access_token, refresh_token=refresh_token, expires_at=expires_at)
    db.add(token)
    db.commit()
    logger.info(f"登录成功 - 用户ID: {user.id}")
    return JSONResponse(content={
        "code": 200, 
        "message": "登录成功", 
        "data": {
            "token": access_token, 
            "refresh_token": refresh_token,
            "expires_at": expires_at
        }
    })

@router.post("/logout", response_model=ResponseModel)
async def logout(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """用户登出，使所有token失效"""
    logger.info(f"登出请求 - 用户ID: {current_user.id}")
    db.query(TokenModel).filter(TokenModel.user_id == current_user.id, TokenModel.is_active == True).update({"is_active": False})
    db.commit()
    logger.info(f"登出成功 - 用户ID: {current_user.id}")
    return JSONResponse(content={
        "code": 200, 
        "message": "退出成功", 
        "data": None
    })

@router.post("/refresh", response_model=ResponseModel)
async def refresh_token(refresh_token: str, db: Session = Depends(get_db)):
    """刷新token，返回新token和refresh_token"""
    logger.info(f"刷新令牌请求 - 令牌: {refresh_token}")
    payload = verify_token(refresh_token)
    if not payload or payload.get("type") != "refresh":
        logger.warning("刷新令牌失败 - 令牌无效或已过期")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="刷新令牌无效或已过期")
    username = payload.get("sub")
    user = db.query(User).filter(User.username == username).first()
    if not user:
        logger.warning(f"刷新令牌失败 - 用户不存在: {username}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="用户不存在")
    
    # 检查refresh_token是否在数据库中且处于活跃状态
    db_token = db.query(TokenModel).filter(
        TokenModel.refresh_token == refresh_token,
        TokenModel.is_active == True
    ).first()
    
    if not db_token:
        logger.warning(f"刷新令牌失败 - 令牌已失效或不存在")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="刷新令牌已失效")
    
    # 将token过期时间设置为24小时
    expires_delta = timedelta(hours=24)
    expires_at = datetime.utcnow() + expires_delta
    access_token = create_access_token(data={"sub": user.username}, expires_delta=expires_delta)
    new_refresh_token = create_access_token(data={"sub": user.username, "type": "refresh"}, expires_delta=timedelta(days=7))
    
    # 将旧token标记为非活跃
    db_token.is_active = False
    db.commit()
    
    # 创建新token
    token = TokenModel(user_id=user.id, access_token=access_token, refresh_token=new_refresh_token, expires_at=expires_at)
    db.add(token)
    db.commit()
    logger.info(f"刷新令牌成功 - 用户ID: {user.id}")
    return JSONResponse(content={
        "code": 200, 
        "message": "刷新成功", 
        "data": {
            "token": access_token, 
            "refresh_token": new_refresh_token,
            "expires_at": expires_at
        }
    })

@router.post("/verify", response_model=ResponseModel)
async def verify(token: str, db: Session = Depends(get_db)):
    """验证token有效性"""
    logger.info(f"验证token有效性 - 令牌: {token}")
    try:
        payload = verify_token(token)
        if not payload:
            logger.warning("验证token失败 - 无效token")
            return JSONResponse(content={
                "code": 401, 
                "message": "无效token", 
                "data": None
            })
        
        # 检查token是否在数据库中且处于活跃状态
        db_token = db.query(TokenModel).filter(
            TokenModel.access_token == token,
            TokenModel.is_active == True,
            TokenModel.expires_at > datetime.utcnow()
        ).first()
        
        if not db_token:
            logger.warning("验证token失败 - token已失效或过期")
            return JSONResponse(content={
                "code": 401, 
                "message": "token已失效或过期", 
                "data": None
            })
        
        logger.info("验证token成功 - token有效")
        return JSONResponse(content={
            "code": 200, 
            "message": "token有效", 
            "data": None
        })
    except Exception as e:
        logger.error(f"验证token出错: {str(e)}")
        return JSONResponse(content={
            "code": 500, 
            "message": "验证token出错", 
            "data": None
        })

@router.delete("/token", response_model=ResponseModel)
async def delete_token(token: str, db: Session = Depends(get_db)):
    """删除指定token"""
    logger.info(f"删除token请求 - 令牌: {token}")
    db_token = db.query(TokenModel).filter(TokenModel.access_token == token).first()
    if not db_token:
        logger.warning("删除token失败 - token不存在")
        return JSONResponse(content={
            "code": 404, 
            "message": "token不存在", 
            "data": None
        })
    db_token.is_active = False
    db.commit()
    logger.info(f"删除token成功 - token已删除")
    return JSONResponse(content={
        "code": 200, 
        "message": "token已删除", 
        "data": None
    })

@router.put("/password", response_model=ResponseModel)
async def change_password(password_change: PasswordChange, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """修改账号密码"""
    logger.info(f"修改密码请求 - 用户ID: {current_user.id}")
    if not verify_password(password_change.old_password, current_user.hashed_password):
        logger.warning(f"修改密码失败 - 旧密码错误 - 用户ID: {current_user.id}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="旧密码错误")
    current_user.hashed_password = get_password_hash(password_change.new_password)
    db.query(TokenModel).filter(TokenModel.user_id == current_user.id, TokenModel.is_active == True).update({"is_active": False})
    db.commit()
    logger.info(f"修改密码成功 - 用户ID: {current_user.id}")
    return JSONResponse(content={
        "code": 200, 
        "message": "密码修改成功", 
        "data": None
    })

@router.post("/cleanup-tokens", response_model=ResponseModel)
async def cleanup_expired_tokens(db: Session = Depends(get_db)):
    """清理过期的token记录
    
    此API端点用于清理数据库中的过期token记录，包括：
    1. 已过期的token
    2. 非活跃状态的token
    3. 过期时间超过30天的token（即使是活跃状态）
    """
    try:
        logger.info("开始清理过期token")
        
        # 清理已过期的token
        expired_count = db.query(TokenModel).filter(
            TokenModel.expires_at < datetime.utcnow()
        ).delete()
        
        # 清理非活跃状态且创建时间超过30天的token
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        inactive_count = db.query(TokenModel).filter(
            TokenModel.is_active == False,
            TokenModel.created_at < thirty_days_ago
        ).delete()
        
        db.commit()
        logger.info(f"清理完成 - 已删除 {expired_count} 个过期token和 {inactive_count} 个非活跃token")
        
        return JSONResponse(content={
            "code": 200,
            "message": "清理成功",
            "data": {
                "expired_tokens_removed": expired_count,
                "inactive_tokens_removed": inactive_count
            }
        })
    except Exception as e:
        logger.error(f"清理token出错: {str(e)}")
        db.rollback()
        return JSONResponse(content={
            "code": 500,
            "message": f"清理token出错: {str(e)}",
            "data": None
        })

# @router.get("/me", response_model=UserWithTokens)
# async def read_users_me(request: Request, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
#     """获取当前用户信息"""
#     logger.info(f"获取用户信息请求 - URL: {request.url} - 用户ID: {current_user.id}")
    
#     # 获取用户的所有有效令牌
#     tokens = db.query(TokenModel).filter(
#         TokenModel.user_id == current_user.id,
#         TokenModel.is_active == True,
#         TokenModel.expires_at > datetime.utcnow()
#     ).all()
    
#     logger.info(f"获取用户信息成功 - 用户ID: {current_user.id}")
#     return {
#         **current_user.__dict__,
#         "tokens": [
#             {
#                 "access_token": token.access_token,
#                 "refresh_token": token.refresh_token,
#                 "token_type": "bearer",
#                 "expires_at": token.expires_at
#             }
#             for token in tokens
#         ]
#     }

# @router.put("/me", response_model=UserResponse)
# async def update_user(
#     request: Request,
#     user_update: UserUpdate,
#     current_user: User = Depends(get_current_user),
#     db: Session = Depends(get_db)
# ):
#     """更新用户信息"""
#     logger.info(f"更新用户信息请求 - URL: {request.url} - 用户ID: {current_user.id} - 更新内容: {user_update.dict()}")
    
#     if user_update.full_name is not None:
#         current_user.full_name = user_update.full_name
#     if user_update.password is not None:
#         current_user.hashed_password = get_password_hash(user_update.password)
    
#     db.commit()
#     db.refresh(current_user)
    
#     logger.info(f"更新用户信息成功 - 用户ID: {current_user.id}")
#     return current_user 