from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from .....core.novel_parser.content_clean import ContentCleaner
from .....core.database.base import get_db
from .....core.database.models import NovelDocument
from fastapi.responses import JSONResponse

router = APIRouter()

@router.post("/content_clean/")
def content_clean(novel_id: int, db: Session = Depends(get_db)):
    novel_document = db.query(NovelDocument).filter(NovelDocument.id == novel_id).first()
    if not novel_document:
        return JSONResponse(status_code=404, content={"msg": "小说不存在"})
    cleaner = ContentCleaner(db)
    result = cleaner.process(novel_document)
    return {"success": result} 