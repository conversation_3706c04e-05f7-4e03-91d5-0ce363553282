from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from .....core.novel_parser.entity_extract import EntityExtractor
from .....core.database.base import get_db
from .....core.database.models import NovelDocument
from fastapi.responses import JSONResponse

router = APIRouter()

@router.post("/entity_extract/")
def entity_extract(novel_id: int, db: Session = Depends(get_db)):
    novel_document = db.query(NovelDocument).filter(NovelDocument.id == novel_id).first()
    if not novel_document:
        return JSONResponse(status_code=404, content={"msg": "小说不存在"})
    extractor = EntityExtractor(db)
    result = extractor.process(novel_document)
    return {"success": result} 