from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from .....core.novel_parser.plot_analyze import PlotAnalyzer
from .....core.database.base import get_db
from .....core.database.models import NovelDocument
from fastapi.responses import JSONResponse

router = APIRouter()

@router.post("/plot_analyze/")
def plot_analyze(novel_id: int, db: Session = Depends(get_db)):
    novel_document = db.query(NovelDocument).filter(NovelDocument.id == novel_id).first()
    if not novel_document:
        return JSONResponse(status_code=404, content={"msg": "小说不存在"})
    analyzer = PlotAnalyzer(db)
    result = analyzer.process(novel_document)
    return {"success": result} 