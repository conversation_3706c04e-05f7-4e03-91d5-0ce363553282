from datetime import datetime
from typing import Optional, List
from pathlib import Path
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from loguru import logger
from enum import Enum
import uuid
import asyncio

from .....core.config import settings
from .....core.database import get_db, SessionLocal
from .....core.utils.security import get_current_user
from .....core.database.models import User, UploadSession, NovelDocument, NovelChapter, NovelEntity

from .....core.task_manager import task_manager
from .....schemas.upload import (
    NovelParseRequest,
    NovelParseResponse,
    NovelParseStatusResponse,
)
from .....schemas.response import ResponseModel
from .....core.novel_parser import NovelParser
from .....core.novel_parser.parse_enums import ParseStep, ParseStatus

router = APIRouter()

# 文件上传目录
UPLOAD_DIR = Path(settings.UPLOAD_DIR)

@router.post("/start", response_model=ResponseModel)
async def start_novel_parsing(
    parse_request: NovelParseRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """开始小说解析任务（只负责初始化数据记录，不执行解析流程）"""
    logger.info(f"用户 {current_user.username} 请求解析小说 - 上传会话ID: {parse_request.upload_session_id}")
    
    # 获取上传会话
    upload_session = db.query(UploadSession).filter(
        UploadSession.id == parse_request.upload_session_id,
        UploadSession.user_id == current_user.id
    ).first()
    
    if not upload_session:
        raise HTTPException(status_code=404, detail="上传会话不存在")
    
    if not upload_session.is_completed:
        raise HTTPException(status_code=400, detail="文件上传尚未完成")
    
    # 检查是否已有解析任务
    existing_document = db.query(NovelDocument).filter(
        NovelDocument.upload_session_id == upload_session.id
    ).first()
    
    if existing_document:
        # 状态1: 已完成
        if existing_document.parse_status == ParseStatus.COMPLETED:
            message = "小说已完成解析，无需重复操作"
            logger.info(message)
            return JSONResponse(content={
                "code": 200,
                "message": message,
                "data": NovelParseResponse.model_validate(existing_document).model_dump()
            })

        # 状态2: 进行中或失败，重置所有任务
        logger.warning(
            f"检测到已有任务，将重置所有解析任务。小说ID: {existing_document.id}, "
            f"当前状态: {existing_document.parse_status}"
        )
        
  
        # 重置小说文档状态
        existing_document.parse_status = ParseStatus.PENDING
        existing_document.parse_error = None
        existing_document.parse_progress = 0.0
        existing_document.current_step = ParseStep.NOT_STARTED
        existing_document.is_parsed = False
        existing_document.parse_started_at = None
        existing_document.parse_completed_at = None
        existing_document.task_id = f"novel_parse_{existing_document.id}_{uuid.uuid4().hex[:8]}"
        

        db.commit()
        
        message = f"小说ID {existing_document.id} 解析任务已重置，请调用各微服务接口进行处理"
        logger.info(message)
        return JSONResponse(content={
            "code": 200,
            "message": message,
            "data": NovelParseResponse.model_validate(existing_document).model_dump()
        })

    # 创建全新的小说文档记录
    novel_document = NovelDocument(
        upload_session_id=upload_session.id,
        title=parse_request.title or upload_session.filename.rsplit(".", 1)[0],
        author=parse_request.author,
        parse_status=ParseStatus.PENDING,
        parse_progress=0.0,
        current_step=ParseStep.NOT_STARTED,
        task_id=f"novel_parse_new_{uuid.uuid4().hex[:8]}"
    )
    db.add(novel_document)
    db.commit()
    db.refresh(novel_document)
    
    
    # 更新任务ID，包含小说ID
    novel_document.task_id = f"novel_parse_{novel_document.id}_{uuid.uuid4().hex[:8]}"
    db.commit()
    
    message = f"小说ID {novel_document.id} 解析任务已初始化，请调用各微服务接口进行处理"
    logger.info(message)
    
    return JSONResponse(content={
        "code": 200,
        "message": message,
        "data": NovelParseResponse.model_validate(novel_document).model_dump()
    })


@router.get("/status", response_model=ResponseModel)
async def get_novel_parse_status(
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    logger.info(f"进入 get_novel_parse_status, decrypted_query: {getattr(request.state, 'decrypted_query', None)}")
    # 获取解密后的 novel_id
    novel_id = getattr(request.state, "decrypted_query", {}).get("novel_id")
    if novel_id is None:
        raise HTTPException(status_code=400, detail="缺少novel_id参数")
    try:
        novel_id = int(novel_id)
    except Exception:
        raise HTTPException(status_code=400, detail="novel_id参数类型错误")
    # 获取小说文档
    novel_document = db.query(NovelDocument).join(
        UploadSession, NovelDocument.upload_session_id == UploadSession.id
    ).filter(
        NovelDocument.id == novel_id,
        UploadSession.user_id == current_user.id
    ).first()
    if not novel_document:
        raise HTTPException(status_code=404, detail="小说文档不存在")
    return JSONResponse(content={
        "code": 200,
        "message": "获取小说解析状态成功",
        "data": NovelParseStatusResponse.model_validate(novel_document).model_dump(exclude_none=True)
    })

# 合并后的 /parse/queue 路由
@router.get("/parse/queue", response_model=ResponseModel)
async def get_queue_info(
    detail: bool = False,
    current_user: User = Depends(get_current_user)
):
    """获取任务队列信息，detail=True 时返回所有任务详情"""
    queue_length = task_manager.get_queue_length()
    running_tasks = task_manager.get_running_tasks_count()
    data = {
        "queue_length": queue_length,
        "running_tasks": running_tasks,
        "max_workers": task_manager.max_workers
    }
    if detail:
        data["tasks"] = task_manager.get_all_tasks()
    return JSONResponse(content={
        "code": 200,
        "message": "获取队列信息成功",
        "data": data
    })

@router.post("/parse/{novel_id}", response_model=ResponseModel)
async def parse_novel(
    novel_id: int,
    db: Session = Depends(get_db)
):
    """
    启动小说解析任务
    
    Args:
        novel_id: 小说ID
        db: 数据库会话
        
    Returns:
        dict: 任务信息
    """
    # 检查小说是否存在
    novel_document = db.query(NovelDocument).filter(NovelDocument.id == novel_id).first()
    if not novel_document:
        raise HTTPException(
            status_code=404,
            detail=f"找不到小说ID: {novel_id}"
        )
    
    # 检查小说是否已经在解析中
    if novel_document.parse_status == ParseStatus.PROCESSING:
        raise HTTPException(
            status_code=400,
            detail=f"小说ID {novel_id} 已经在解析中"
        )
    
    # 生成任务ID
    task_id = str(uuid.uuid4())
    
    # 提交解析任务
    success = task_manager.submit_task(
        task_id=task_id,
        task_type="novel_parse",
        func=process_novel_parsing,
        novel_id=novel_id
    )
    
    if not success:
        raise HTTPException(
            status_code=500,
            detail="提交任务失败"
        )
    
    message = "解析任务已提交"
    logger.info(f"{message} - novel_id: {novel_id}, task_id: {task_id}")
    logger.info("准备创建 JSONResponse")
    response = JSONResponse(content={
        "code": 200,
        "message": message,
        "data": {
            "task_id": task_id,
            "novel_id": novel_id,
            "status": "submitted"
        }
    })
    logger.info("已创建 JSONResponse，准备 return")
    return response

@router.get("/parse/status", response_model=ResponseModel)
async def get_parse_status(request: Request):
    logger.info(f"进入 get_parse_status, decrypted_query: {getattr(request.state, 'decrypted_query', None)}")
    """
    获取解析任务状态（参数通过加密q传递）
    Args:
        task_id: 任务ID
    Returns:
        dict: 任务状态信息
    """
    task_id = getattr(request.state, "decrypted_query", {}).get("task_id")
    if not task_id:
        raise HTTPException(status_code=400, detail="缺少task_id参数")
    # 获取任务状态
    task_info = task_manager.get_task_status(task_id)
    if not task_info:
        raise HTTPException(
            status_code=404,
            detail=f"找不到任务ID: {task_id}"
        )
    # 获取队列信息
    queue_length = task_manager.get_queue_length()
    running_tasks = task_manager.get_running_tasks_count()
    # 计算队列位置
    queue_position = None
    if task_info["status"] == "queued":
        # 遍历队列找到位置
        all_tasks = task_manager.get_all_tasks()
        for i, task in enumerate(all_tasks.values()):
            if task["id"] == task_id:
                queue_position = i + 1
                break
    message = "获取任务状态成功"
    return JSONResponse(content={
        "code": 200,
        "message": message,
        "data": {
            "task_id": task_id,
            "status": task_info["status"],
            "result": task_info.get("result"),
            "error": task_info.get("error"),
            "queue_info": {
                "position": queue_position,
                "total_queued": queue_length,
                "running_tasks": running_tasks
            }
        }
    })


@router.post("/parse/retry/{novel_id}", response_model=ResponseModel)
async def retry_parse_task(
    novel_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """重置并自动提交解析任务"""
    novel_document = db.query(NovelDocument).join(
        UploadSession, NovelDocument.upload_session_id == UploadSession.id
    ).filter(
        NovelDocument.id == novel_id,
        UploadSession.user_id == current_user.id
    ).first()
    if not novel_document:
        raise HTTPException(status_code=404, detail="小说文档不存在")
    # 重置状态
    import uuid
    novel_document.parse_status = ParseStatus.PENDING
    novel_document.parse_error = None
    novel_document.parse_progress = 0.0
    novel_document.current_step = ParseStep.NOT_STARTED
    novel_document.is_parsed = False
    novel_document.parse_started_at = None
    novel_document.parse_completed_at = None
    novel_document.task_id = f"novel_parse_{novel_document.id}_{uuid.uuid4().hex[:8]}"
    db.commit()
    db.refresh(novel_document)
    # 提交新任务
    task_id = str(uuid.uuid4())
    success = task_manager.submit_task(
        task_id=task_id,
        task_type="novel_parse",
        func=process_novel_parsing,
        novel_id=novel_id
    )
    if not success:
        raise HTTPException(status_code=500, detail="提交任务失败")
    return JSONResponse(content={
        "code": 200,
        "message": f"小说ID {novel_document.id} 解析任务已重置并重新提交",
        "data": {
            "task_id": task_id,
            "novel_id": novel_id,
            "status": "submitted"
        }
    })

def process_novel_parsing(novel_id: int):
    """处理小说解析的后台任务（多线程安全）"""
    db = SessionLocal()
    try:
        parser = NovelParser(db, novel_id)
        success = parser.parse()  # 改为同步调用
        if success:
            logger.info(f"小说解析任务成功完成 - ID: {novel_id}")
            return {"status": "success", "novel_id": novel_id}
        else:
            logger.error(f"小说解析任务失败 - ID: {novel_id}")
            return {"status": "failed", "novel_id": novel_id}
    except Exception as e:
        logger.exception(f"小说解析任务异常 - ID: {novel_id}, 错误: {str(e)}")
        try:
            novel_document = db.query(NovelDocument).filter(NovelDocument.id == novel_id).first()
            if novel_document:
                novel_document.parse_status = ParseStatus.FAILED
                novel_document.parse_error = str(e)
                db.commit()
        except:
            pass
        return {"status": "error", "novel_id": novel_id, "error": str(e)}
    finally:
        db.close() 