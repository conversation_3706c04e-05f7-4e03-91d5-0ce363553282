import asyncio
import time
import uuid
from typing import AsyncGenerator, Dict, List, Optional

from fastapi import APIRouter, Depends, Request
from fastapi.responses import StreamingResponse

from ....core.llm import create_agent
from ....core.config.settings import settings
from ....schemas.chat import ChatCompletionRequest, ChatCompletionChunk, Choice, Delta


router = APIRouter()

@router.post("/chat", summary="流式聊天接口")
async def stream_chat(
    request: ChatCompletionRequest,
    # 在这里可以添加依赖注入，例如获取当前用户
    # current_user: User = Depends(get_current_active_user),
):
    """
    处理流式聊天请求，并以 Server-Sent Events (SSE) 的形式返回响应。
    """
    if not request.stream:
        return {"error": "Non-streaming requests are not supported in this example."}

    # 使用新的Agent架构
    try:
        agent = create_agent("general_chat")
        if not agent:
            return {"error": "Chat agent not available"}

        # 简化的聊天响应（非流式）
        messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]
        response = await agent.chat(messages[-1]["content"] if messages else "Hello")

        return {"choices": [{"message": {"role": "assistant", "content": response}}]}
    except Exception as e:
        return {"error": f"Chat service error: {str(e)}"}
