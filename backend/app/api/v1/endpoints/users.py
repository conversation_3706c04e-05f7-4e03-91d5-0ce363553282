from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime

from ....core.database import get_db
from ....core.utils.security import get_current_user
from ....core.database.models.user import User
from ....core.database.models.token import Token
from ....schemas.user import UserResponse
from ....schemas.auth import UserWithTokens


router = APIRouter()

@router.get("/me", response_model=UserWithTokens)
def read_user_me(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取当前登录用户信息
    """
    # 获取用户的所有有效令牌
    tokens = db.query(Token).filter(
        Token.user_id == current_user.id,
        Token.is_active == True,
        Token.expires_at > datetime.utcnow()
    ).all()
    
    return {
        **current_user.__dict__,
        "tokens": [
            {
                "access_token": token.access_token,
                "refresh_token": token.refresh_token,
                "token_type": "bearer",
                "expires_at": token.expires_at
            }
            for token in tokens
        ]
    }

@router.get("/{user_id}", response_model=UserResponse)
def read_user(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取指定用户信息
    """
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user 