from fastapi import APIRouter
from .upload import router as upload_router
from .auth import router as auth_router
from .endpoints.novel_tasks.novel_parse import router as novel_parse_router
from .endpoints.chat import router as chat_router
from .endpoints.novel_tasks.content_clean_service import router as content_clean_router
from .endpoints.novel_tasks.entity_extract_service import router as entity_extract_router
from .endpoints.novel_tasks.relationship_analyze_service import router as relationship_analyze_router
from .endpoints.novel_tasks.timeline_build_service import router as timeline_build_router
from .endpoints.novel_tasks.plot_analyze_service import router as plot_analyze_router
from .endpoints import users

api_router = APIRouter()

api_router.include_router(auth_router, prefix="/auth", tags=["auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(upload_router, prefix="/upload", tags=["upload"])
api_router.include_router(chat_router, prefix="/chat", tags=["chat"])

# 注册小说解析相关路由
api_router.include_router(novel_parse_router, prefix="/novel/parse", tags=["novel_parse"])
api_router.include_router(content_clean_router, prefix="/novel/content-clean", tags=["content_clean"])
api_router.include_router(entity_extract_router, prefix="/novel/entity-extract", tags=["entity_extract"])
api_router.include_router(relationship_analyze_router, prefix="/novel/relationship-analyze", tags=["relationship_analyze"])
api_router.include_router(timeline_build_router, prefix="/novel/timeline-build", tags=["timeline_build"])
api_router.include_router(plot_analyze_router, prefix="/novel/plot-analyze", tags=["plot_analyze"])

__all__ = ['api_router'] 