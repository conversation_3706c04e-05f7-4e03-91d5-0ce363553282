import os
import hashlib
from typing import List, Optional
from pathlib import Path
from fastapi import APIRouter, Depends, File, UploadFile, HTTPException, Form, Body, Query
from sqlalchemy.orm import Session
from loguru import logger
from ...core.config import settings 
from ...core.database import get_db
from ...core.utils.security import get_current_user
from ...core.database.models import User, UploadSession, UploadChunk
from ...schemas.upload import (
    UploadSessionCreate,
    UploadSessionResponse,
    ChunkUploadResponse,
    UploadSessionStatus,
    UserUploadListResponse,
    UserUploadListItem
)

router = APIRouter()

# 文件上传目录
UPLOAD_DIR = Path(settings.UPLOAD_DIR)
CHUNK_DIR = UPLOAD_DIR / "chunks"  # 分块存储目录

# 确保目录存在
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)
CHUNK_DIR.mkdir(parents=True, exist_ok=True)

def calculate_hash(data: bytes) -> str:
    """计算数据的MD5哈希值"""
    return hashlib.md5(data).hexdigest()

@router.post("/init", response_model=UploadSessionResponse)
async def init_upload_session(
    session_data: UploadSessionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """初始化文件上传会话"""
    # 计算总分块数
    total_chunks = (session_data.file_size + session_data.chunk_size - 1) // session_data.chunk_size
    
    # 创建会话目录
    session_dir = CHUNK_DIR / str(current_user.id)
    session_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建上传会话记录
    upload_session = UploadSession(
        filename=session_data.filename,
        file_size=session_data.file_size,
        chunk_size=session_data.chunk_size,
        total_chunks=total_chunks,
        temp_dir=str(session_dir),
        user_id=current_user.id
    )
    db.add(upload_session)
    db.commit()
    db.refresh(upload_session)
    
    # 创建分块记录
    chunks = [
        UploadChunk(session_id=upload_session.id, chunk_index=i, chunk_hash="")
        for i in range(total_chunks)
    ]
    db.bulk_save_objects(chunks)
    db.commit()
    
    return upload_session

@router.post("/chunk/{session_id}", response_model=ChunkUploadResponse)
async def upload_chunk(
    session_id: int,
    chunk_index: int = Form(...),
    chunk_hash: str = Form(...),
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """上传文件分块"""
    # 获取上传会话
    session = db.query(UploadSession).filter(
        UploadSession.id == session_id,
        UploadSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="上传会话不存在")
    
    if session.is_completed:
        raise HTTPException(status_code=400, detail="该上传会话已完成")
    
    if chunk_index >= session.total_chunks:
        raise HTTPException(status_code=400, detail="分块序号无效")
    
    # 读取分块数据
    chunk_data = await file.read()
    server_chunk_hash = calculate_hash(chunk_data)
    
    if server_chunk_hash != chunk_hash:
        raise HTTPException(status_code=400, detail="分块哈希值不匹配")
    
    # 保存分块文件
    temp_dir = Path(session.temp_dir)
    chunk_file = temp_dir / f"{chunk_index}.chunk"
    chunk_file.write_bytes(chunk_data)
    
    # 更新分块状态
    chunk = db.query(UploadChunk).filter(
        UploadChunk.session_id == session_id,
        UploadChunk.chunk_index == chunk_index
    ).first()
    
    chunk.chunk_hash = server_chunk_hash
    chunk.is_uploaded = True
    
    # 更新已上传分块数
    session.uploaded_chunks += 1
    
    # 检查是否所有分块都已上传
    if session.uploaded_chunks == session.total_chunks:
        session.is_completed = True
        # 合并文件
        await merge_chunks(session)
    
    db.commit()
    
    return ChunkUploadResponse(
        session_id=session_id,
        chunk_index=chunk_index,
        is_uploaded=True,
        message="分块上传成功"
    )

@router.get("/status/{session_id}", response_model=UploadSessionStatus)
async def get_upload_status(
    session_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取上传会话状态"""
    session = db.query(UploadSession).filter(
        UploadSession.id == session_id,
        UploadSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="上传会话不存在")
    
    # 获取未上传的分块
    missing_chunks = [
        chunk.chunk_index
        for chunk in session.chunks
        if not chunk.is_uploaded
    ]
    
    # 计算上传进度
    progress = (session.uploaded_chunks / session.total_chunks) * 100
    
    return UploadSessionStatus(
        session_id=session.id,
        filename=session.filename,
        total_chunks=session.total_chunks,
        uploaded_chunks=session.uploaded_chunks,
        is_completed=session.is_completed,
        upload_progress=progress,
        missing_chunks=missing_chunks
    )

@router.post("/textfile")
async def upload_text_file(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """上传txt等文本文件，保存到本地临时文件夹（需token认证）"""
    # 只允许文本文件
    if not file.filename.lower().endswith(tuple(settings.ALLOWED_UPLOAD_EXTENSIONS)):
        raise HTTPException(status_code=400, detail=f"只允许上传以下格式的文件: {', '.join(settings.ALLOWED_UPLOAD_EXTENSIONS)}")
    
    save_path = UPLOAD_DIR / file.filename
    # 防止重名覆盖
    i = 1
    while save_path.exists():
        name_parts = os.path.splitext(file.filename)
        save_path = UPLOAD_DIR / f"{name_parts[0]}_{i}{name_parts[1]}"
        i += 1
    
    content = await file.read()
    save_path.write_bytes(content)
    
    return {
        "code": 200,
        "message": "上传成功",
        "data": {
            "filename": save_path.name,
            "filepath": str(save_path)
        }
    }

@router.get("/list")
async def get_user_uploads(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    is_completed: Optional[bool] = Query(None, description="是否已完成"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户上传列表"""
    logger.info(f"用户 {current_user.username} 请求获取上传列表 - 页码: {page}, 每页数量: {page_size}, 完成状态: {is_completed}")
    
    query = db.query(UploadSession).filter(UploadSession.user_id == current_user.id)
    
    # 根据是否完成进行过滤
    if is_completed is not None:
        query = query.filter(UploadSession.is_completed == is_completed)
        logger.debug(f"过滤条件: is_completed = {is_completed}")
    
    # 获取总数
    total = query.count()
    logger.debug(f"查询到总记录数: {total}")
    
    # 分页
    query = query.order_by(UploadSession.created_at.desc())
    query = query.offset((page - 1) * page_size).limit(page_size)
    
    # 获取数据
    uploads = query.all()
    logger.debug(f"当前页记录数: {len(uploads)}")
    
    # 转换为响应格式
    items = []
    for upload in uploads:
        # 计算上传进度
        progress = (upload.uploaded_chunks / upload.total_chunks) * 100 if upload.total_chunks > 0 else 0
        items.append(UserUploadListItem(
            id=upload.id,
            filename=upload.filename,
            file_size=upload.file_size,
            is_completed=upload.is_completed,
            upload_progress=progress,
            created_at=upload.created_at,
            updated_at=upload.updated_at
        ))
    
    response_data = UserUploadListResponse(
        total=total,
        items=items
    )
    
    # 输出响应数据以便调试
    logger.debug(f"响应数据结构: {type(response_data)}")
    logger.debug(f"响应数据内容: {response_data.model_dump_json()}")
    
    logger.info(f"成功返回上传列表数据 - 用户: {current_user.username}, 总数: {total}, 当前页记录数: {len(items)}")
    
    # 按照统一规范返回数据
    from fastapi.responses import JSONResponse
    return JSONResponse(content={
        "code": 200,
        "message": "获取上传列表成功",
        "data": {
            "total": total,
            "items": [item.model_dump() for item in items]
        }
    })

async def merge_chunks(session: UploadSession):
    """合并文件分块"""
    temp_dir = Path(session.temp_dir)
    output_file = UPLOAD_DIR / session.filename
    
    with output_file.open("wb") as out:
        for i in range(session.total_chunks):
            chunk_file = temp_dir / f"{i}.chunk"
            out.write(chunk_file.read_bytes())
            # 删除分块文件
            chunk_file.unlink()
    
    # 删除临时目录
    temp_dir.rmdir() 