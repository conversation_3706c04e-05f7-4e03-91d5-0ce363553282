# 基础配置
ENV=development
DEBUG=true
PROJECT_NAME=AI绘本与短视频创作平台
VERSION=0.1.0
API_V1_STR=/api/v1
LOG_LEVEL=DEBUG

# 服务器配置
HOST=127.0.0.1
PORT=8001
WORKERS=4

# 数据库配置
INIT_DB=True
DATABASE_TYPE=sqlite
DATABASE_URL=postgresql://postgres:13569813617Ch@localhost:5432/tinytales_dev
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_POOL_SIZE=10

# JWT配置
SECRET_KEY=d97f4d5fa482bd897aa980fa65992fa867ff73d61b9466f45083170c5773abe9
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI服务配置
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_API_BASE=https://api.openai.com/v1
QIANWEN_API_KEY=sk-45d77088274048be8ad0ee334ac3cae0
QIANWEN_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
DEFAULT_AI_SERVICE=qianwen
DEFAULT_OPENAI_MODEL=gpt-4-turbo-preview
DEFAULT_QIANWEN_MODEL=qwen-turbo

# 安全配置
ALLOWED_HOSTS=["*"]
CORS_ORIGINS=["*"]
RATE_LIMIT=0

# 缓存配置
CACHE_TTL=3600
CACHE_PREFIX=tinytales_dev_

# CDN配置
CDN_ENABLED=false
CDN_BASE_URL=

# AES加密配置
AES_KEY=HVJVXfixSsto2xndnpp8/FtFUPAdvGPIoDkryG3TuRI=
AES_IV=aiil7bqQ1cALMKIHpautFg==
AES_ENCRYPTION_VERSION=1
AES_ENCRYPTION_ENABLED=true

# 数据库配置
INIT_DB=true  # 开发环境默认初始化数据库