# Python 标准库导入
from typing import TypeVar, Generic, Optional, Any

# 第三方库导入
from pydantic import BaseModel, Field

# 类型变量定义
T = TypeVar('T')

# 响应模型定义
class ResponseModel(BaseModel, Generic[T]):
    """统一API响应模型"""
    code: int = Field(200, description="状态码")
    message: str = Field("success", description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")

    class Config:
        json_schema_extra = {
            "example": {
                "code": 200,
                "message": "success",
                "data": {
                    "id": 1,
                    "name": "example"
                }
            }
        }

class ErrorResponse(BaseModel):
    """错误响应模型"""
    code: int = Field(..., description="错误码")
    message: str = Field(..., description="错误信息")
    data: Optional[Any] = Field(None, description="响应数据")

    class Config:
        json_schema_extra = {
            "example": {
                "code": 400,
                "message": "请求参数验证失败：username: 用户名长度必须大于5个字符；password: 密码长度必须大于8个字符",
                "data": None
            }
        } 