import uuid
import time
from typing import List, Optional, Dict
from pydantic import BaseModel, Field

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[Dict[str, str]]
    stream: bool = False

class Delta(BaseModel):
    content: Optional[str] = None

class Choice(BaseModel):
    delta: Delta
    index: int
    finish_reason: Optional[str] = None

class ChatCompletionChunk(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4().hex}")
    object: str = "chat.completion.chunk"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[Choice] 