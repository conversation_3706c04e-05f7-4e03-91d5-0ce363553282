"""
认证相关的数据模型
包括登录、令牌和OAuth2认证
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field

# 登录相关
class UserLogin(BaseModel):
    username: str = Field(..., description="用户名或邮箱，两者均可用于登录")
    password: str = Field(..., description="用户密码", min_length=8)

class OAuth2Login(BaseModel):
    provider: str
    code: str
    redirect_uri: Optional[str] = None

# 令牌相关
class Token(BaseModel):
    token: str
    refresh_token: str
    token_type: str = "bearer"

class TokenPayload(BaseModel):
    sub: Optional[str] = None
    type: Optional[str] = None

# 带令牌的用户信息
class UserWithTokens(BaseModel):
    id: int
    username: str
    email: EmailStr
    token: str
    refresh_token: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class PasswordChange(BaseModel):
    old_password: str
    new_password: str 