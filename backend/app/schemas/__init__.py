"""
数据模型模式模块
提供API请求和响应的数据验证模型
"""

# 用户相关模型
from .user import (
    UserBase,
    UserCreate,
    UserUpdate,
    UserResponse
)

# 认证相关模型
from .auth import (
    UserLogin,
    OAuth2Login,
    Token,
    TokenPayload,
    UserWithTokens
)

# 故事相关模型
from .story_model import (
    CharacterModel,
    SceneModel,
    StoryOutlineModel,
    StoryConfigModel,
    Directive,
    Input,
    AskRequest,
    ProjectRequest
)

__all__ = [
    # 用户模型
    'UserBase',
    'UserCreate',
    'UserUpdate',
    'UserResponse',
    
    # 认证模型
    'UserLogin',
    'OAuth2Login',
    'Token',
    'TokenPayload',
    'UserWithTokens',
    
    # 故事相关模型
    'CharacterModel',
    'SceneModel',
    'StoryOutlineModel',
    'StoryConfigModel',
    'Directive',
    'Input',
    'AskRequest',
    'ProjectRequest'
] 