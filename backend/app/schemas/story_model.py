from pydantic import BaseModel
from typing import List, Literal
from typing import Optional
class CharacterModel(BaseModel):
    name: str = ""
    type: str = ""
    trait: str = ""
    age: str = ""
    description: str = ""

class SceneModel(BaseModel):
    place: str = ""
    time: str = ""
    description:str = ""


class StoryOutlineModel(BaseModel):
    theme: str = ""  
    # 故事主题（中心思想），比如："学会分享"、"勇敢表达自己"、"团队合作"。
    # 这一项是最核心的教育意义。

    starting_point: str = ""
    # 故事开端，描述主角的初始状态或现状。
    # 比如："小兔子总是害怕与别人分享自己的食物。"

    trigger_event: str = ""
    # 触发事件，指引起故事变化的关键事件。
    # 比如："寒冬来临，森林里的朋友们食物短缺。"

    emotional_reaction: str = ""
    # 主角对触发事件的第一情绪反应，展示情绪变化。
    # 比如："小兔子感到害怕又犹豫，不知道该不该帮忙。"

    growth_process: str = ""
    # 主角在故事中成长和变化的过程。
    # 比如："在朋友们互相分享的影响下，小兔子尝试给出一根胡萝卜。"

    climax: str = ""
    # 故事高潮，主角面临的最大挑战或最重要的选择时刻。
    # 比如："一只受伤的小鸟需要食物，小兔子必须决定是否分享更多。"

    resolution: str = ""
    # 最后主角如何解决问题，故事如何收尾。
    # 比如："小兔子慷慨分享了食物，大家温暖度过了寒冬。"

    lesson: str = ""
    # 从整个故事中总结出的道理或启发，给孩子们的教育意义。
    # 比如："分享不仅帮助别人，也带来更多的友情和快乐。"

class StoryConfigModel(BaseModel):
    story_outline: StoryOutlineModel = StoryOutlineModel() #故事大纲
    age_range: str = "" #年龄范围
    gender:str = "" #性别
    main_character: CharacterModel = CharacterModel() #主角
    side_characters: List[CharacterModel] = [] #配角
    scene:List[SceneModel]=[] #场景
class Directive(BaseModel): #指令
    field: str #字段
    label: str #标签

class Input(BaseModel):
    message: str
class AskRequest(BaseModel):
    message: str #用户对话信息
    storyConfig:StoryConfigModel #故事配置
    directives: Optional[List[Directive]] = None #指令
class ProjectRequest(BaseModel):
    storyConfig:StoryConfigModel #故事配置
    project_name:str #项目名称
    project_type:str #项目类型