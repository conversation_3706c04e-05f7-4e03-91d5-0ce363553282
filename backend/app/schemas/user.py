"""
用户相关的数据模型
包括用户基础信息、创建、更新和响应模型
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field
from ..core.database.models.user import AuthProvider

# 用户基础模型
class UserBase(BaseModel):
    username: str
    email: EmailStr
    nickname: Optional[str] = None
    is_active: Optional[bool] = True
    auth_provider: Optional[AuthProvider] = AuthProvider.LOCAL

# 用户创建模型
class UserCreate(UserBase):
    password: str = Field(..., min_length=8, max_length=20)
    username: str = Field(..., min_length=5, max_length=20)

# 用户更新模型
class UserUpdate(BaseModel):
    nickname: Optional[str] = None
    old_password: Optional[str] = None
    new_password: Optional[str] = Field(None, min_length=8, max_length=20)

# 用户响应模型
class UserResponse(UserBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

