from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List, Dict, Any

class UploadSessionCreate(BaseModel):
    """创建上传会话的请求模型"""
    filename: str
    file_size: int
    chunk_size: int = Field(default=5 * 1024 * 1024)  # 默认5MB

class UploadSessionResponse(BaseModel):
    """上传会话的响应模型"""
    id: int
    filename: str
    file_size: int
    chunk_size: int
    total_chunks: int
    uploaded_chunks: int
    is_completed: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ChunkUploadResponse(BaseModel):
    """分块上传的响应模型"""
    session_id: int
    chunk_index: int
    is_uploaded: bool
    message: str

class UploadSessionStatus(BaseModel):
    """上传会话状态的响应模型"""
    session_id: int
    filename: str
    total_chunks: int
    uploaded_chunks: int
    is_completed: bool
    upload_progress: float  # 上传进度（百分比）
    missing_chunks: List[int]  # 未上传的分块序号列表

class UserUploadListItem(BaseModel):
    """用户上传列表项模型"""
    id: int
    filename: str
    file_size: int
    is_completed: bool
    upload_progress: float
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class UserUploadListResponse(BaseModel):
    """用户上传列表响应模型"""
    total: int
    items: List[UserUploadListItem]

# 小说解析相关模型
class NovelParseRequest(BaseModel):
    """小说解析请求模型"""
    upload_session_id: int
    title: Optional[str] = None
    author: Optional[str] = None

class NovelParseResponse(BaseModel):
    """小说解析响应模型"""
    id: int
    upload_session_id: int
    title: Optional[str] = None
    author: Optional[str] = None
    parse_status: str
    
    class Config:
        from_attributes = True

class NovelParseStatusResponse(BaseModel):
    """小说解析状态响应模型"""
    id: int
    upload_session_id: int
    title: Optional[str] = None
    author: Optional[str] = None
    is_parsed: bool
    parse_status: str
    parse_progress: float
    current_step: str
    
    # 解析步骤状态
    step_chapter_split: bool
    step_content_clean: bool
    step_entity_extract: bool
    step_relationship_analyze: bool
    step_timeline_build: bool
    step_plot_analyze: bool
    
    # 时间信息
    parse_started_at: Optional[datetime] = None
    parse_completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class NovelChapterResponse(BaseModel):
    """小说章节响应模型"""
    id: int
    chapter_index: int
    title: Optional[str] = None
    content: str
    word_count: int
    is_processed: bool
    
    class Config:
        from_attributes = True

class NovelChapterListResponse(BaseModel):
    """小说章节列表响应模型"""
    total: int
    items: List[NovelChapterResponse]

class NovelEntityResponse(BaseModel):
    """小说实体响应模型"""
    id: int
    entity_type: str
    name: str
    mentions: int
    first_appearance: Optional[int] = None
    attributes: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True

class NovelEntityListResponse(BaseModel):
    """小说实体列表响应模型"""
    total: int
    items: List[NovelEntityResponse] 