"""
新架构基础对话流程测试
专注测试新LLM架构的核心功能 - 交互式菜单版本
"""

import asyncio
import sys
import os
import time
from pathlib import Path
from prettytable import PrettyTable

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger

# 配置日志
logger.remove()
logger.add(
    "logs/test_llm_chat.log",
    rotation="10 MB",
    retention="1 week",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="INFO",
    encoding="utf-8"
)
logger.add(sys.stderr, level="WARNING")  # 控制台只显示警告以上级别

def test_configuration():
    """测试配置"""
    logger.info("开始测试配置")
    print("\n=== 测试配置 ===")

    try:
        from app.core.config.settings import settings

        # 创建配置信息表格
        table = PrettyTable()
        table.field_names = ["配置项", "状态", "值"]
        table.align = "l"

        table.add_row(["默认AI服务", "✓", settings.DEFAULT_AI_SERVICE])
        table.add_row(["Qwen API", "✓" if settings.QIANWEN_API_KEY else "✗", "已配置" if settings.QIANWEN_API_KEY else "未配置"])
        table.add_row(["Ollama API", "✓", settings.OLLAMA_API_BASE])
        table.add_row(["CephalonQwen API", "✓" if settings.CEPHALONQWEN_API_KEY else "✗", "已配置" if settings.CEPHALONQWEN_API_KEY else "未配置"])
        table.add_row(["数据库类型", "✓", settings.DATABASE_TYPE])
        table.add_row(["加密功能", "✓" if settings.AES_ENCRYPTION_ENABLED else "✗", "启用" if settings.AES_ENCRYPTION_ENABLED else "禁用"])

        print(table)
        logger.info("配置测试成功")
        return True

    except Exception as e:
        logger.error(f"配置测试失败: {e}")
        print(f"✗ 配置测试失败: {e}")
        return False

def test_single_llm_provider(provider_name):
    """测试单个LLM提供者"""
    logger.info(f"开始测试{provider_name}提供者")
    print(f"\n=== 测试{provider_name}提供者 ===")

    try:
        from app.core.llm.llm_providers import get_llm_provider
        from langchain.schema import HumanMessage

        # 创建LLM
        print(f"正在创建{provider_name} LLM...")
        llm = get_llm_provider(provider_name)
        print(f"✓ {provider_name} LLM创建成功")

        # 测试调用
        print("正在测试API调用...")
        test_message = "你好，请用一句话介绍自己" if provider_name != "ollama" else "Hello, introduce yourself briefly"
        message = HumanMessage(content=test_message)

        start_time = time.time()
        response = llm.invoke([message])
        end_time = time.time()

        # 创建结果表格
        table = PrettyTable()
        table.field_names = ["项目", "结果"]
        table.align = "l"

        table.add_row(["提供者", provider_name])
        table.add_row(["状态", "✓ 成功"])
        table.add_row(["响应时间", f"{end_time - start_time:.2f}秒"])
        table.add_row(["响应长度", f"{len(response.content)}字符"])
        table.add_row(["响应内容", response.content[:100] + "..." if len(response.content) > 100 else response.content])

        print(table)
        logger.info(f"{provider_name}提供者测试成功")
        return True

    except Exception as e:
        logger.error(f"{provider_name}提供者测试失败: {e}")
        print(f"✗ {provider_name}提供者测试失败: {e}")
        return False

def test_all_llm_providers():
    """测试所有LLM提供者"""
    logger.info("开始测试所有LLM提供者")
    print("\n=== 测试所有LLM提供者 ===")

    providers = ["qwen", "ollama", "cephalon_qwen"]
    results = []

    # 创建结果表格
    table = PrettyTable()
    table.field_names = ["提供者", "状态", "备注"]
    table.align = "l"

    for provider in providers:
        try:
            success = test_single_llm_provider(provider)
            results.append(success)
            table.add_row([provider, "✓ 成功" if success else "✗ 失败", "正常" if success else "需要检查配置"])
        except Exception as e:
            results.append(False)
            table.add_row([provider, "✗ 失败", str(e)[:50]])

    print("\n=== 提供者测试汇总 ===")
    print(table)

    success_count = sum(results)
    print(f"\n总计: {success_count}/{len(providers)} 个提供者测试通过")

    return any(results)  # 至少一个成功就算通过

def test_enhanced_agent_client(provider="qwen", use_tools=False):
    """测试增强Agent客户端"""
    logger.info(f"开始测试增强Agent客户端 - 提供者: {provider}")
    print(f"\n=== 测试增强Agent客户端 ({provider}) ===")

    try:
        from app.core.llm.agent_client import EnhancedAgentClient

        print("正在创建增强Agent客户端...")
        # 创建客户端
        client = EnhancedAgentClient(
            llm_provider=provider,
            use_default_tools=use_tools,
            memory_type="simple",
            verbose=False
        )
        print("✓ EnhancedAgentClient创建成功")

        # 获取客户端信息
        info = client.get_agent_info()

        # 创建信息表格
        table = PrettyTable()
        table.field_names = ["属性", "值"]
        table.align = "l"

        table.add_row(["LLM类型", info['llm_type']])
        table.add_row(["工具数量", info['tools_count']])
        table.add_row(["记忆类型", info['memory_type']])
        table.add_row(["Agent类型", info['agent_type']])
        table.add_row(["详细输出", "是" if info['verbose'] else "否"])

        if info['tools_count'] > 0:
            table.add_row(["工具列表", ", ".join(info['tool_names'][:3]) + ("..." if len(info['tool_names']) > 3 else "")])

        print("\n=== Agent信息 ===")
        print(table)

        # 测试对话
        print("\n正在测试对话功能...")
        test_question = "你好，请简单介绍一下自己"
        start_time = time.time()
        response = client.chat(test_question)
        end_time = time.time()

        # 创建对话结果表格
        chat_table = PrettyTable()
        chat_table.field_names = ["项目", "结果"]
        chat_table.align = "l"

        chat_table.add_row(["问题", test_question])
        chat_table.add_row(["响应时间", f"{end_time - start_time:.2f}秒"])
        chat_table.add_row(["响应长度", f"{len(response)}字符"])
        chat_table.add_row(["响应内容", response[:150] + "..." if len(response) > 150 else response])

        print("\n=== 对话测试结果 ===")
        print(chat_table)

        # 测试记忆功能
        print("\n正在测试记忆功能...")
        summary = client.get_memory_summary()
        print(f"记忆摘要: {summary[:100]}..." if len(summary) > 100 else summary)

        logger.info("增强Agent客户端测试成功")
        return True

    except Exception as e:
        logger.error(f"增强Agent客户端测试失败: {e}")
        print(f"✗ 增强Agent客户端测试失败: {e}")
        return False

def test_memory_functionality():
    """测试记忆功能"""
    logger.info("开始测试记忆功能")
    print("\n=== 测试记忆功能 ===")

    try:
        from app.core.llm.memory import create_memory_manager
        from langchain.schema import HumanMessage, AIMessage

        # 测试不同类型的记忆管理器
        memory_types = ["simple", "conversation"]

        for memory_type in memory_types:
            print(f"\n测试{memory_type}记忆管理器...")

            # 创建记忆管理器
            memory = create_memory_manager(memory_type)
            print(f"✓ {memory_type}记忆管理器创建成功")

            # 添加测试消息
            test_messages = [
                HumanMessage(content="我叫张三"),
                AIMessage(content="你好张三，很高兴认识你！"),
                HumanMessage(content="我刚才说我叫什么？"),
                AIMessage(content="您刚才说您叫张三。")
            ]

            for msg in test_messages:
                memory.add_message(msg)

            # 获取消息历史
            messages = memory.get_messages()

            # 创建结果表格
            table = PrettyTable()
            table.field_names = ["属性", "值"]
            table.align = "l"

            table.add_row(["记忆类型", memory_type])
            table.add_row(["存储消息数", len(messages)])
            table.add_row(["最新消息", messages[-1].content if messages else "无"])

            if hasattr(memory, 'get_conversation_summary'):
                summary = memory.get_conversation_summary()
                table.add_row(["对话摘要", summary[:50] + "..." if len(summary) > 50 else summary])

            print(table)

        logger.info("记忆功能测试成功")
        return True

    except Exception as e:
        logger.error(f"记忆功能测试失败: {e}")
        print(f"✗ 记忆功能测试失败: {e}")
        return False

def test_prompt_templates():
    """测试Prompt模板"""
    logger.info("开始测试Prompt模板")
    print("\n=== 测试Prompt模板 ===")

    try:
        from app.core.llm.prompts import format_prompt, get_prompt_template, prompt_manager

        # 获取所有可用模板
        template_names = prompt_manager.get_template_names()

        # 创建模板列表表格
        table = PrettyTable()
        table.field_names = ["模板名称", "状态", "测试结果"]
        table.align = "l"

        test_templates = ["general_chat", "code_generation", "translation"]

        for template_name in test_templates:
            try:
                # 测试获取模板
                template = get_prompt_template(template_name)
                if template:
                    # 测试格式化
                    if template_name == "general_chat":
                        formatted = format_prompt(template_name, question="什么是人工智能？")
                    elif template_name == "code_generation":
                        formatted = format_prompt(template_name, language="Python", requirements="打印Hello World", style="简洁")
                    elif template_name == "translation":
                        formatted = format_prompt(template_name, source_language="中文", target_language="英文", text="你好")
                    else:
                        formatted = "跳过格式化测试"

                    table.add_row([template_name, "✓ 可用", "✓ 格式化成功"])
                else:
                    table.add_row([template_name, "✗ 不可用", "✗ 获取失败"])

            except Exception as e:
                table.add_row([template_name, "✗ 错误", str(e)[:30]])

        print(table)
        print(f"\n总计可用模板数: {len(template_names)}")

        logger.info("Prompt模板测试成功")
        return True

    except Exception as e:
        logger.error(f"Prompt模板测试失败: {e}")
        print(f"✗ Prompt模板测试失败: {e}")
        return False

def interactive_chat_demo(provider="qwen"):
    """交互式对话演示"""
    logger.info(f"开始交互式对话演示 - 提供者: {provider}")
    print(f"\n=== 交互式对话演示 ({provider}) ===")

    try:
        from app.core.llm.agent_client import EnhancedAgentClient

        # 创建客户端
        client = EnhancedAgentClient(
            llm_provider=provider,
            use_default_tools=False,
            memory_type="conversation",
            verbose=False
        )

        print("✓ 对话客户端创建成功")
        print("输入 'quit' 或 'exit' 退出对话")
        print("输入 'memory' 查看对话记忆")
        print("输入 'clear' 清空对话记忆")
        print("-" * 50)

        while True:
            try:
                user_input = input("\n👤 您: ").strip()

                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见！")
                    break
                elif user_input.lower() == 'memory':
                    summary = client.get_memory_summary()
                    print(f"📝 对话记忆:\n{summary}")
                    continue
                elif user_input.lower() == 'clear':
                    client.clear_memory()
                    print("🧹 对话记忆已清空")
                    continue
                elif not user_input:
                    continue

                # 发送消息并获取响应
                print("🤖 AI正在思考...")
                start_time = time.time()
                response = client.chat(user_input)
                end_time = time.time()

                print(f"🤖 AI ({end_time - start_time:.1f}s): {response}")

            except KeyboardInterrupt:
                print("\n👋 对话已中断")
                break
            except Exception as e:
                print(f"❌ 对话出错: {e}")

        return True

    except Exception as e:
        logger.error(f"交互式对话演示失败: {e}")
        print(f"❌ 交互式对话演示失败: {e}")
        return False

def main():
    """主菜单函数"""
    logger.info("===== 开始LLM架构测试 =====")
    print("===== 新架构LLM对话测试 =====")

    try:
        while True:
            print("\n" + "="*50)
            print("🧪 LLM架构测试菜单")
            print("="*50)
            print("1. 测试系统配置")
            print("2. 测试单个LLM提供者")
            print("3. 测试所有LLM提供者")
            print("4. 测试记忆功能")
            print("5. 测试Prompt模板")
            print("6. 测试增强Agent客户端")
            print("7. 交互式对话演示")
            print("8. 运行完整测试套件")
            print("0. 退出")

            choice = input("\n请选择功能 (0-8): ").strip()

            if choice == "1":
                test_configuration()

            elif choice == "2":
                print("\n可用的LLM提供者:")
                print("1. qwen")
                print("2. ollama")
                print("3. cephalon_qwen")

                provider_choice = input("请选择提供者 (1-3): ").strip()
                provider_map = {"1": "qwen", "2": "ollama", "3": "cephalon_qwen"}

                if provider_choice in provider_map:
                    test_single_llm_provider(provider_map[provider_choice])
                else:
                    print("❌ 无效选择")

            elif choice == "3":
                test_all_llm_providers()

            elif choice == "4":
                test_memory_functionality()

            elif choice == "5":
                test_prompt_templates()

            elif choice == "6":
                print("\n选择LLM提供者:")
                print("1. qwen")
                print("2. ollama")
                print("3. cephalon_qwen")

                provider_choice = input("请选择提供者 (1-3): ").strip()
                provider_map = {"1": "qwen", "2": "ollama", "3": "cephalon_qwen"}

                if provider_choice in provider_map:
                    use_tools = input("是否启用工具? (y/n): ").lower() == 'y'
                    test_enhanced_agent_client(provider_map[provider_choice], use_tools)
                else:
                    print("❌ 无效选择")

            elif choice == "7":
                print("\n选择LLM提供者:")
                print("1. qwen")
                print("2. ollama")
                print("3. cephalon_qwen")

                provider_choice = input("请选择提供者 (1-3): ").strip()
                provider_map = {"1": "qwen", "2": "ollama", "3": "cephalon_qwen"}

                if provider_choice in provider_map:
                    interactive_chat_demo(provider_map[provider_choice])
                else:
                    print("❌ 无效选择")

            elif choice == "8":
                print("\n🚀 运行完整测试套件...")

                # 创建测试结果表格
                table = PrettyTable()
                table.field_names = ["测试项目", "状态", "备注"]
                table.align = "l"

                tests = [
                    ("配置测试", test_configuration),
                    ("LLM提供者", test_all_llm_providers),
                    ("记忆功能", test_memory_functionality),
                    ("Prompt模板", test_prompt_templates),
                    ("Agent客户端", lambda: test_enhanced_agent_client("qwen", False))
                ]

                passed = 0
                for test_name, test_func in tests:
                    try:
                        result = test_func()
                        status = "✅ 通过" if result else "❌ 失败"
                        note = "正常" if result else "需要检查"
                        table.add_row([test_name, status, note])
                        if result:
                            passed += 1
                    except Exception as e:
                        table.add_row([test_name, "❌ 错误", str(e)[:30]])

                print("\n" + "="*50)
                print("📊 完整测试结果")
                print("="*50)
                print(table)
                print(f"\n总计: {passed}/{len(tests)} 个测试通过")

                if passed == len(tests):
                    print("🎉 所有测试通过！系统运行正常。")
                else:
                    print("⚠️  部分测试失败，请检查配置。")

            elif choice == "0":
                print("👋 退出测试程序")
                break

            else:
                print("❌ 无效选择，请重试")

    except KeyboardInterrupt:
        print("\n👋 程序已中断")
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        print(f"❌ 程序执行出错: {str(e)}")

    logger.info("===== LLM架构测试结束 =====")
    print("\n===== 测试结束 =====")

if __name__ == "__main__":
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)

    # 运行主菜单
    main()
