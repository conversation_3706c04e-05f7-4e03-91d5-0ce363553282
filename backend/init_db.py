"""
手动初始化数据库脚本
用于创建所有数据库表
"""

# 确保导入所有模型
from app.core.database.models.user import User, AuthProvider
from app.core.database.models.token import Token
from app.core.database import init_db
from loguru import logger

def main():
    """执行数据库初始化"""
    logger.info("开始初始化数据库...")
    try:
        init_db()
        logger.success("数据库初始化成功!")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        raise

if __name__ == "__main__":
    main() 