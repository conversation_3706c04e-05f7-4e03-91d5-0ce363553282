"""
检查SQLite数据库文件的路径和状态
"""

import os
import sqlite3
from app.core.config import settings
from loguru import logger

def main():
    """检查SQLite数据库路径和文件"""
    # 获取设置中的数据库URL
    db_url = settings.DATABASE_URL
    logger.info(f"设置中的数据库URL: {db_url}")
    
    # 从URL中提取文件路径
    if db_url.startswith("sqlite:///"):
        # 绝对路径
        file_path = db_url[10:]
    elif db_url.startswith("sqlite://"):
        # 相对路径
        file_path = db_url[9:]
    else:
        logger.error(f"不是SQLite URL: {db_url}")
        return
    
    logger.info(f"提取的数据库文件路径: {file_path}")
    
    # 检查文件是否存在
    if os.path.exists(file_path):
        logger.info(f"数据库文件存在: {file_path}")
        # 获取文件大小
        size = os.path.getsize(file_path)
        logger.info(f"文件大小: {size} 字节")
        
        # 检查是否可读写
        if os.access(file_path, os.R_OK):
            logger.info("文件可读")
        else:
            logger.warning("文件不可读")
        
        if os.access(file_path, os.W_OK):
            logger.info("文件可写")
        else:
            logger.warning("文件不可写")
        
        # 尝试连接数据库并查询表
        try:
            conn = sqlite3.connect(file_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            if tables:
                logger.info(f"数据库中的表: {', '.join(table[0] for table in tables)}")
            else:
                logger.warning("数据库中没有表")
            
            conn.close()
        except Exception as e:
            logger.error(f"连接数据库出错: {str(e)}")
    else:
        logger.warning(f"数据库文件不存在: {file_path}")
        # 检查目录是否存在且可写
        dir_path = os.path.dirname(file_path)
        if dir_path and not os.path.exists(dir_path):
            logger.error(f"数据库目录不存在: {dir_path}")
        elif not os.access(dir_path, os.W_OK):
            logger.error(f"数据库目录不可写: {dir_path}")
        else:
            logger.info(f"数据库目录存在并可写: {dir_path}")
    
    # 检查可能的其他路径
    app_path = os.path.abspath(os.path.join("app", "app.db"))
    root_path = os.path.abspath("app.db")
    
    logger.info(f"检查可能的其他路径:")
    if os.path.exists(app_path):
        logger.info(f"发现数据库文件: {app_path}")
    if os.path.exists(root_path):
        logger.info(f"发现数据库文件: {root_path}")

if __name__ == "__main__":
    main() 