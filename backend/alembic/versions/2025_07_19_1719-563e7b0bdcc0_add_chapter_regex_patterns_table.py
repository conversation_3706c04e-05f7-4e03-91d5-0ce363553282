"""add chapter_regex_patterns table

Revision ID: 563e7b0bdcc0
Revises: 8ca5737c9cb9
Create Date: 2025-07-19 17:19:28.254054

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '563e7b0bdcc0'
down_revision: Union[str, None] = '8ca5737c9cb9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chapter_regex_patterns',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('pattern_type', sa.String(length=32), nullable=False),
    sa.Column('pattern', sa.Text(), nullable=False),
    sa.Column('source', sa.String(length=32), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.Column('hit_count', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_hit_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chapter_regex_patterns_id'), 'chapter_regex_patterns', ['id'], unique=False)
    op.create_table('novel_documents',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('upload_session_id', sa.Integer(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('author', sa.String(), nullable=True),
    sa.Column('task_id', sa.String(), nullable=True),
    sa.Column('is_parsed', sa.Boolean(), nullable=True),
    sa.Column('parse_status', sa.String(), nullable=True),
    sa.Column('parse_progress', sa.Float(), nullable=True),
    sa.Column('parse_error', sa.Text(), nullable=True),
    sa.Column('current_step', sa.String(), nullable=True),
    sa.Column('step_chapter_split', sa.Boolean(), nullable=True),
    sa.Column('step_chapter_split_at', sa.DateTime(), nullable=True),
    sa.Column('step_content_clean', sa.Boolean(), nullable=True),
    sa.Column('step_content_clean_at', sa.DateTime(), nullable=True),
    sa.Column('step_entity_extract', sa.Boolean(), nullable=True),
    sa.Column('step_entity_extract_at', sa.DateTime(), nullable=True),
    sa.Column('step_relationship_analyze', sa.Boolean(), nullable=True),
    sa.Column('step_relationship_analyze_at', sa.DateTime(), nullable=True),
    sa.Column('step_timeline_build', sa.Boolean(), nullable=True),
    sa.Column('step_timeline_build_at', sa.DateTime(), nullable=True),
    sa.Column('step_plot_analyze', sa.Boolean(), nullable=True),
    sa.Column('step_plot_analyze_at', sa.DateTime(), nullable=True),
    sa.Column('parse_started_at', sa.DateTime(), nullable=True),
    sa.Column('parse_completed_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['upload_session_id'], ['upload_sessions.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('upload_session_id')
    )
    op.create_index(op.f('ix_novel_documents_id'), 'novel_documents', ['id'], unique=False)
    op.create_index(op.f('ix_novel_documents_task_id'), 'novel_documents', ['task_id'], unique=False)
    op.create_table('fuzzy_chapter_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('novel_document_id', sa.Integer(), nullable=False),
    sa.Column('analyzed_content_snippet', sa.Text(), nullable=False),
    sa.Column('returned_regex', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['novel_document_id'], ['novel_documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_fuzzy_chapter_logs_id'), 'fuzzy_chapter_logs', ['id'], unique=False)
    op.create_table('novel_chapters',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('novel_document_id', sa.Integer(), nullable=True),
    sa.Column('chapter_index', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('word_count', sa.Integer(), nullable=True),
    sa.Column('is_processed', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['novel_document_id'], ['novel_documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_novel_chapters_id'), 'novel_chapters', ['id'], unique=False)
    op.create_table('novel_entities',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('novel_document_id', sa.Integer(), nullable=True),
    sa.Column('entity_type', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('mentions', sa.Integer(), nullable=True),
    sa.Column('first_appearance', sa.Integer(), nullable=True),
    sa.Column('attributes', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['novel_document_id'], ['novel_documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_novel_entities_id'), 'novel_entities', ['id'], unique=False)
    op.create_table('novel_parse_task',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('novel_id', sa.Integer(), nullable=False),
    sa.Column('task_type', sa.Enum('chapter_split', 'content_clean', 'entity_extract', 'relationship_analyze', 'timeline_build', 'plot_analyze', name='parsetasktype'), nullable=False),
    sa.Column('status', sa.Enum('pending', 'processing', 'completed', 'failed', name='parsetaskstatus'), nullable=False),
    sa.Column('progress', sa.Float(), nullable=True),
    sa.Column('error', sa.Text(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['novel_id'], ['novel_documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_novel_parse_task_novel_id'), 'novel_parse_task', ['novel_id'], unique=False)
    op.create_table('novel_plots',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('novel_document_id', sa.Integer(), nullable=False),
    sa.Column('plot_type', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('start_chapter', sa.Integer(), nullable=True),
    sa.Column('end_chapter', sa.Integer(), nullable=True),
    sa.Column('importance', sa.Float(), nullable=True),
    sa.Column('related_entities', sa.JSON(), nullable=True),
    sa.Column('related_events', sa.JSON(), nullable=True),
    sa.Column('attributes', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['novel_document_id'], ['novel_documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_novel_plots_id'), 'novel_plots', ['id'], unique=False)
    op.create_table('novel_timelines',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('novel_document_id', sa.Integer(), nullable=False),
    sa.Column('event_title', sa.String(), nullable=False),
    sa.Column('event_description', sa.Text(), nullable=True),
    sa.Column('chapter_index', sa.Integer(), nullable=True),
    sa.Column('event_order', sa.Integer(), nullable=False),
    sa.Column('event_time', sa.String(), nullable=True),
    sa.Column('related_entities', sa.JSON(), nullable=True),
    sa.Column('attributes', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['novel_document_id'], ['novel_documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_novel_timelines_id'), 'novel_timelines', ['id'], unique=False)
    op.create_table('novel_relationships',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('source_entity_id', sa.Integer(), nullable=False),
    sa.Column('target_entity_id', sa.Integer(), nullable=False),
    sa.Column('relation_type', sa.String(), nullable=False),
    sa.Column('attributes', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['source_entity_id'], ['novel_entities.id'], ),
    sa.ForeignKeyConstraint(['target_entity_id'], ['novel_entities.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_novel_relationships_id'), 'novel_relationships', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_novel_relationships_id'), table_name='novel_relationships')
    op.drop_table('novel_relationships')
    op.drop_index(op.f('ix_novel_timelines_id'), table_name='novel_timelines')
    op.drop_table('novel_timelines')
    op.drop_index(op.f('ix_novel_plots_id'), table_name='novel_plots')
    op.drop_table('novel_plots')
    op.drop_index(op.f('ix_novel_parse_task_novel_id'), table_name='novel_parse_task')
    op.drop_table('novel_parse_task')
    op.drop_index(op.f('ix_novel_entities_id'), table_name='novel_entities')
    op.drop_table('novel_entities')
    op.drop_index(op.f('ix_novel_chapters_id'), table_name='novel_chapters')
    op.drop_table('novel_chapters')
    op.drop_index(op.f('ix_fuzzy_chapter_logs_id'), table_name='fuzzy_chapter_logs')
    op.drop_table('fuzzy_chapter_logs')
    op.drop_index(op.f('ix_novel_documents_task_id'), table_name='novel_documents')
    op.drop_index(op.f('ix_novel_documents_id'), table_name='novel_documents')
    op.drop_table('novel_documents')
    op.drop_index(op.f('ix_chapter_regex_patterns_id'), table_name='chapter_regex_patterns')
    op.drop_table('chapter_regex_patterns')
    # ### end Alembic commands ###
