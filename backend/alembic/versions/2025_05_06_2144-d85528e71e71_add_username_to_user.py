"""add username to user

Revision ID: d85528e71e71
Revises: 5baf7ff2418d
Create Date: 2025-05-06 21:44:55.512589

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import table, column
from sqlalchemy import String, Integer, Boolean, DateTime, Enum


# revision identifiers, used by Alembic.
revision: str = 'd85528e71e71'
down_revision: Union[str, None] = '5baf7ff2418d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # 防止上次迁移失败导致 users_new 残留
    op.execute("DROP TABLE IF EXISTS users_new")
    # 创建新表 users_new，hashed_password 可空，新增字段，provider_id 唯一
    op.create_table(
        'users_new',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('hashed_password', sa.String(), nullable=True),
        sa.Column('full_name', sa.String(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_superuser', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('username', sa.String(), nullable=False),
        sa.Column('nickname', sa.String(), nullable=True),
        sa.Column('auth_provider', sa.Enum('local', 'google', 'github', 'wechat', name='authprovider'), nullable=True),
        sa.Column('provider_id', sa.String(), unique=True, nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    # 拷贝原表数据
    op.execute("""
        INSERT INTO users_new (id, email, hashed_password, full_name, is_active, is_superuser, created_at, updated_at, username, nickname, auth_provider, provider_id)
        SELECT id, email, hashed_password, full_name, is_active, is_superuser, created_at, updated_at, username, nickname, auth_provider, provider_id FROM users
    """)
    # 删除原表
    op.drop_table('users')
    # 重命名新表
    op.rename_table('users_new', 'users')
    # 重新创建索引
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    # op.create_unique_constraint(None, 'users', ['provider_id'])  # 已在建表时唯一

def downgrade():
    # 创建旧表结构（hashed_password 不可空，无新增字段）
    op.create_table(
        'users_old',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('hashed_password', sa.String(), nullable=False),
        sa.Column('full_name', sa.String(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_superuser', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    # 拷贝数据回旧表（只拷贝原有字段）
    op.execute("""
        INSERT INTO users_old (id, email, hashed_password, full_name, is_active, is_superuser, created_at, updated_at)
        SELECT id, email, COALESCE(hashed_password, ''), full_name, is_active, is_superuser, created_at, updated_at FROM users
    """)
    # 删除现表
    op.drop_table('users')
    # 重命名
    op.rename_table('users_old', 'users')
    # 重新创建索引
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
