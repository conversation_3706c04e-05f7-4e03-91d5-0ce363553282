"""merge heads

Revision ID: 827a38fa2697
Revises: 202507200000, 563e7b0bdcc0
Create Date: 2025-07-19 21:19:21.507555

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '827a38fa2697'
down_revision: Union[str, None] = ('202507200000', '563e7b0bdcc0')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
