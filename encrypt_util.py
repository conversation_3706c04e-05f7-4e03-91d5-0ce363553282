import base64
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

def encrypt(data: dict, key_b64: str, iv_b64: str) -> str:
    key = base64.b64decode(key_b64)
    iv = base64.b64decode(iv_b64)
    raw = json.dumps(data, ensure_ascii=False).encode("utf-8")
    cipher = AES.new(key, AES.MODE_CBC, iv)
    encrypted = cipher.encrypt(pad(raw, AES.block_size))
    return base64.b64encode(encrypted).decode("utf-8")

def decrypt(data_b64: str, key_b64: str, iv_b64: str) -> dict:
    key = base64.b64decode(key_b64)
    iv = base64.b64decode(iv_b64)
    encrypted = base64.b64decode(data_b64)
    cipher = AES.new(key, AES.MODE_CBC, iv)
    decrypted = unpad(cipher.decrypt(encrypted), AES.block_size)
    return json.loads(decrypted.decode("utf-8")) 