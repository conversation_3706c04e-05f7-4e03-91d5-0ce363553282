"""
小说解析测试框架
使用真实文件和项目原有数据库，直接调用核心模块进行测试
"""

import sys
import os
import uuid
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from loguru import logger
from datetime import datetime

# 添加项目根目录到Python路径
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent
sys.path.insert(0, str(project_root))

# 直接使用项目原有配置
from backend.app.core.database.base import get_db, engine
from backend.app.core.database.models import (
    Base, NovelDocument, UploadSession, NovelChapter, 
    User, ChapterRegexPattern, FuzzyChapterLog
)
from backend.app.core.novel_parser.parse_enums import ParseStep, ParseStatus
from backend.app.core.novel_parser.chapter_split import ChapterSplitter
from backend.app.core.config.settings import settings


class NovelParseTestFramework:
    """小说解析测试框架 - 使用项目原有配置"""
    
    def __init__(self):
        self.db: Optional[Session] = None
        self.test_user: Optional[User] = None
        
        # 真实目录配置
        self.novel_source_dir = Path("tests/novels/source")  # 待上传的小说目录
        self.novel_uploaded_dir = Path("tests/novels/uploaded")  # 上传完成的小说目录
        self.novel_processed_dir = Path("tests/novels/processed")  # 处理完成的小说目录
        
        # 创建目录
        self.novel_source_dir.mkdir(parents=True, exist_ok=True)
        self.novel_uploaded_dir.mkdir(parents=True, exist_ok=True)
        self.novel_processed_dir.mkdir(parents=True, exist_ok=True)
        
        # 当前工作数据
        self.current_novel_file: Optional[Path] = None
        self.current_upload_session: Optional[UploadSession] = None
        self.current_novel_document: Optional[NovelDocument] = None
        
        self.available_operations = {
            "1": {
                "name": "环境管理",
                "operations": {
                    "1": ("初始化数据库", self.init_database),
                    "2": ("查看目录状态", self.show_directory_status),
                    "3": ("清理处理数据", self.cleanup_processed_data),
                    "4": ("查看数据库状态", self.show_database_status),
                }
            },
            "2": {
                "name": "文件管理",
                "operations": {
                    "1": ("列出待上传小说", self.list_source_novels),
                    "2": ("选择小说文件", self.select_novel_file),
                    "3": ("上传小说文件", self.upload_novel_file),
                    "4": ("查看已上传文件", self.list_uploaded_novels),
                }
            },
            "3": {
                "name": "解析任务管理",
                "operations": {
                    "1": ("创建解析任务", self.create_parse_task),
                    "2": ("获取解析状态", self.get_novel_parse_status),
                    "3": ("查看章节结果", self.show_chapter_results),
                    "4": ("导出章节文件", self.export_chapters),
                }
            },
            "4": {
                "name": "微服务调用测试",
                "operations": {
                    "1": ("章节分割服务", self.call_chapter_split_service),
                    "2": ("内容清洗服务", self.call_content_clean_service),
                    "3": ("实体提取服务", self.call_entity_extract_service),
                    "4": ("完整解析流程", self.run_full_parse_pipeline),
                }
            },
            "5": {
                "name": "章节分割专项测试",
                "operations": {
                    "1": ("测试章节识别", self.test_chapter_recognition),
                    "2": ("查看分割规则", self.show_split_rules),
                    "3": ("重新分割章节", self.re_split_chapters),
                    "4": ("章节质量检查", self.check_chapter_quality),
                }
            }
        }
    
    def init_database(self):
        """初始化数据库 - 使用项目原有配置"""
        print("🔧 初始化数据库...")
        try:
            # 显示当前数据库配置
            print(f"数据库URL: {settings.DATABASE_URL}")
            print(f"数据库类型: {settings.DATABASE_TYPE}")
            
            # 使用项目原有的数据库引擎和配置
            Base.metadata.create_all(bind=engine)
            
            # 获取数据库会话
            self.db = next(get_db())
            
            # 创建或获取测试用户
            self.test_user = self.db.query(User).filter(User.username == "test_user").first()
            if not self.test_user:
                self.test_user = User(
                    username="test_user",
                    email="<EMAIL>",
                    hashed_password="test_password_hash",
                    is_active=True
                )
                self.db.add(self.test_user)
                self.db.commit()
                self.db.refresh(self.test_user)
                print(f"✅ 创建测试用户: {self.test_user.username}")
            else:
                print(f"✅ 使用现有测试用户: {self.test_user.username}")
            
            print(f"✅ 数据库初始化成功 (用户ID: {self.test_user.id})")
            return True
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            logger.exception("数据库初始化异常")
            return False
    
    def show_directory_status(self):
        """显示目录状态"""
        print("📁 目录状态:")
        
        directories = [
            ("待上传小说", self.novel_source_dir),
            ("已上传小说", self.novel_uploaded_dir),
            ("处理完成", self.novel_processed_dir)
        ]
        
        for name, path in directories:
            if path.exists():
                files = list(path.glob("*.txt"))
                print(f"  {name}: {path} ({len(files)} 个文件)")
                for file in files[:5]:  # 只显示前5个
                    size = file.stat().st_size
                    print(f"    - {file.name} ({size} 字节)")
                if len(files) > 5:
                    print(f"    ... 还有 {len(files) - 5} 个文件")
            else:
                print(f"  {name}: {path} (目录不存在)")
    
    def cleanup_processed_data(self):
        """清理处理数据"""
        print("🧹 清理处理数据...")
        
        confirm = input("确认清理所有处理数据? (y/N): ").strip().lower()
        if confirm != 'y':
            print("取消清理")
            return
        
        try:
            if not self.db:
                print("❌ 数据库未初始化")
                return
            
            # 清理数据库中的解析数据
            deleted_chapters = self.db.query(NovelChapter).delete()
            deleted_patterns = self.db.query(ChapterRegexPattern).delete()
            deleted_logs = self.db.query(FuzzyChapterLog).delete()
            deleted_documents = self.db.query(NovelDocument).delete()
            deleted_sessions = self.db.query(UploadSession).delete()
            
            self.db.commit()
            
            # 清理处理完成目录
            if self.novel_processed_dir.exists():
                shutil.rmtree(self.novel_processed_dir)
                self.novel_processed_dir.mkdir(exist_ok=True)
            
            print(f"✅ 清理完成:")
            print(f"  - 删除章节: {deleted_chapters}")
            print(f"  - 删除规则: {deleted_patterns}")
            print(f"  - 删除日志: {deleted_logs}")
            print(f"  - 删除文档: {deleted_documents}")
            print(f"  - 删除会话: {deleted_sessions}")
            
            # 重置当前状态
            self.current_novel_file = None
            self.current_upload_session = None
            self.current_novel_document = None
            
        except Exception as e:
            print(f"❌ 清理失败: {e}")
            self.db.rollback()
    
    def show_database_status(self):
        """显示数据库状态"""
        print("📊 数据库状态:")
        try:
            if not self.db:
                print("❌ 数据库未初始化")
                return
            
            tables_info = [
                ("用户", User),
                ("上传会话", UploadSession),
                ("小说文档", NovelDocument),
                ("章节数据", NovelChapter),
                ("分割规则", ChapterRegexPattern),
                ("模糊日志", FuzzyChapterLog),
            ]
            
            for name, model in tables_info:
                count = self.db.query(model).count()
                print(f"  - {name}: {count} 条记录")
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
    
    def list_source_novels(self):
        """列出待上传的小说"""
        print("📚 待上传小说列表:")
        
        txt_files = list(self.novel_source_dir.glob("*.txt"))
        
        if not txt_files:
            print(f"  {self.novel_source_dir} 目录下没有 .txt 文件")
            print(f"  请将小说文件放入该目录")
            return
        
        for i, file in enumerate(txt_files, 1):
            size = file.stat().st_size
            modified = datetime.fromtimestamp(file.stat().st_mtime)
            print(f"  {i:2d}. {file.name}")
            print(f"      大小: {size:,} 字节")
            print(f"      修改: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
    
    def select_novel_file(self):
        """选择小说文件"""
        print("📖 选择小说文件:")
        
        txt_files = list(self.novel_source_dir.glob("*.txt"))
        
        if not txt_files:
            print("❌ 没有可选择的小说文件")
            return False
        
        # 显示文件列表
        for i, file in enumerate(txt_files, 1):
            size = file.stat().st_size
            print(f"  {i}. {file.name} ({size:,} 字节)")
        
        try:
            choice = int(input(f"请选择文件 (1-{len(txt_files)}): ").strip())
            if 1 <= choice <= len(txt_files):
                self.current_novel_file = txt_files[choice - 1]
                print(f"✅ 已选择: {self.current_novel_file.name}")
                return True
            else:
                print("❌ 无效选择")
                return False
        except ValueError:
            print("❌ 请输入数字")
            return False
    
    def upload_novel_file(self):
        """上传小说文件 - 模拟真实上传流程"""
        print("📤 上传小说文件...")
        
        if not self.current_novel_file:
            print("❌ 请先选择小说文件")
            return False
        
        if not self.db or not self.test_user:
            print("❌ 请先初始化数据库")
            return False
        
        try:
            # 读取文件内容
            content = self.current_novel_file.read_text(encoding='utf-8')
            file_size = len(content.encode('utf-8'))
            
            # 复制文件到上传目录（模拟上传完成）
            uploaded_file = self.novel_uploaded_dir / self.current_novel_file.name
            shutil.copy2(self.current_novel_file, uploaded_file)
            
            # 创建上传会话记录（使用项目原有模型）
            upload_session = UploadSession(
                filename=self.current_novel_file.name,
                file_size=file_size,
                chunk_size=1024*1024,  # 1MB
                total_chunks=1,
                uploaded_chunks=1,
                temp_dir=str(self.novel_uploaded_dir),
                is_completed=True,
                user_id=self.test_user.id
            )
            
            self.db.add(upload_session)
            self.db.commit()
            self.db.refresh(upload_session)
            
            self.current_upload_session = upload_session
            
            print(f"✅ 文件上传成功:")
            print(f"  - 源文件: {self.current_novel_file}")
            print(f"  - 上传文件: {uploaded_file}")
            print(f"  - 大小: {file_size:,} 字节")
            print(f"  - 会话ID: {upload_session.id}")
            
            return True
            
        except Exception as e:
            print(f"❌ 上传失败: {e}")
            self.db.rollback()
            return False
    
    def list_uploaded_novels(self):
        """查看已上传文件"""
        print("📋 已上传文件:")
        
        if not self.db:
            print("❌ 数据库未初始化")
            return
        
        sessions = self.db.query(UploadSession).filter(
            UploadSession.user_id == self.test_user.id
        ).order_by(UploadSession.created_at.desc()).all()
        
        if not sessions:
            print("  没有已上传的文件")
            return
        
        for session in sessions:
            status = "✅" if session.is_completed else "🔄"
            print(f"  {status} {session.filename}")
            print(f"      ID: {session.id}")
            print(f"      大小: {session.file_size:,} 字节")
            print(f"      上传时间: {session.created_at}")
            
            # 检查是否有解析任务
            novel_doc = self.db.query(NovelDocument).filter(
                NovelDocument.upload_session_id == session.id
            ).first()
            
            if novel_doc:
                print(f"      解析状态: {novel_doc.parse_status}")
                print(f"      解析进度: {novel_doc.parse_progress}%")
    
    def create_parse_task(self):
        """创建解析任务 - 复制start_novel_parsing的核心逻辑"""
        print("📋 创建解析任务...")
        
        if not self.current_upload_session:
            print("❌ 请先上传小说文件")
            return False
        
        try:
            # 输入小说信息
            title = input(f"小说标题 (默认: {self.current_upload_session.filename}): ").strip()
            author = input("作者 (默认: 未知作者): ").strip()
            
            # 模拟 NovelParseRequest
            parse_request_data = {
                "upload_session_id": self.current_upload_session.id,
                "title": title if title else None,
                "author": author if author else None
            }
            
            # === 复制 start_novel_parsing 的核心逻辑 ===
            logger.info(f"用户 {self.test_user.username} 请求解析小说 - 上传会话ID: {parse_request_data['upload_session_id']}")
            
            # 获取上传会话 (已经有了，但重新验证)
            upload_session = self.db.query(UploadSession).filter(
                UploadSession.id == parse_request_data['upload_session_id'],
                UploadSession.user_id == self.test_user.id
            ).first()
            
            if not upload_session:
                print("❌ 上传会话不存在")
                return False
            
            if not upload_session.is_completed:
                print("❌ 文件上传尚未完成")
                return False
            
            # 检查是否已有解析任务
            existing_document = self.db.query(NovelDocument).filter(
                NovelDocument.upload_session_id == upload_session.id
            ).first()
            
            if existing_document:
                # 状态1: 已完成
                if existing_document.parse_status == ParseStatus.COMPLETED:
                    message = "小说已完成解析，无需重复操作"
                    logger.info(message)
                    print(f"✅ {message}")
                    print(f"  - 小说ID: {existing_document.id}")
                    print(f"  - 标题: {existing_document.title}")
                    self.current_novel_document = existing_document
                    return True

                # 状态2: 进行中或失败，重置所有任务
                logger.warning(
                    f"检测到已有任务，将重置所有解析任务。小说ID: {existing_document.id}, "
                    f"当前状态: {existing_document.parse_status}"
                )
                
                print(f"⚠️  检测到已有任务 (ID: {existing_document.id}, 状态: {existing_document.parse_status})")
                overwrite = input("是否重置现有任务? (y/N): ").strip().lower()
                if overwrite != 'y':
                    self.current_novel_document = existing_document
                    print(f"✅ 使用现有任务 (ID: {existing_document.id})")
                    return True
                
                # 重置小说文档状态
                existing_document.parse_status = ParseStatus.PENDING
                existing_document.parse_error = None
                existing_document.parse_progress = 0.0
                existing_document.current_step = ParseStep.NOT_STARTED
                existing_document.is_parsed = False
                existing_document.parse_started_at = None
                existing_document.parse_completed_at = None
                existing_document.task_id = f"novel_parse_{existing_document.id}_{uuid.uuid4().hex[:8]}"
                
                self.db.commit()
                
                message = f"小说ID {existing_document.id} 解析任务已重置，请调用各微服务接口进行处理"
                logger.info(message)
                print(f"✅ {message}")
                
                self.current_novel_document = existing_document
                return True

            # 创建全新的小说文档记录
            novel_document = NovelDocument(
                upload_session_id=upload_session.id,
                title=parse_request_data['title'] or upload_session.filename.rsplit(".", 1)[0],
                author=parse_request_data['author'],
                parse_status=ParseStatus.PENDING,
                parse_progress=0.0,
                current_step=ParseStep.NOT_STARTED,
                task_id=f"novel_parse_new_{uuid.uuid4().hex[:8]}"
            )
            self.db.add(novel_document)
            self.db.commit()
            self.db.refresh(novel_document)
            
            # 更新任务ID，包含小说ID
            novel_document.task_id = f"novel_parse_{novel_document.id}_{uuid.uuid4().hex[:8]}"
            self.db.commit()
            
            message = f"小说ID {novel_document.id} 解析任务已初始化，请调用各微服务接口进行处理"
            logger.info(message)
            print(f"✅ {message}")
            
            self.current_novel_document = novel_document
            
            print(f"📊 任务详情:")
            print(f"  - 小说ID: {novel_document.id}")
            print(f"  - 标题: {novel_document.title}")
            print(f"  - 作者: {novel_document.author}")
            print(f"  - 任务ID: {novel_document.task_id}")
            print(f"  - 状态: {novel_document.parse_status}")
            print(f"  - 当前步骤: {novel_document.current_step}")
            
            return True
            
        except Exception as e:
            print(f"❌ 任务创建失败: {e}")
            logger.exception("创建解析任务异常")
            self.db.rollback()
            return False
    
    def execute_chapter_split(self):
        """执行章节分割 - 直接调用项目原有的ChapterSplitter"""
        print("🔪 执行章节分割...")
        
        if not self.current_novel_document:
            print("❌ 请先创建解析任务")
            return False
        
        try:
            # 获取文件路径
            uploaded_file = self.novel_uploaded_dir / self.current_upload_session.filename
            
            if not uploaded_file.exists():
                print(f"❌ 文件不存在: {uploaded_file}")
                return False
            
            # 读取文件内容
            content = uploaded_file.read_text(encoding='utf-8')
            
            # 使用项目原有的章节分割器
            splitter = ChapterSplitter(self.db)
            
            print(f"📖 开始分割小说: {self.current_novel_document.title}")
            print(f"📄 文件大小: {len(content):,} 字符")
            
            # 创建内容生成器（模拟真实的分块上传）
            def content_chunks_factory():
                yield content
            
            # 更新状态为处理中
            self.current_novel_document.parse_status = ParseStatus.PROCESSING
            self.current_novel_document.current_step = ParseStep.CHAPTER_SPLIT_STARTED
            self.current_novel_document.parse_progress = 10.0
            self.db.commit()
            
            # 执行分割（调用项目原有方法）
            result = splitter.process(self.current_novel_document, content_chunks_factory)
            
            if result:
                # 更新状态
                self.current_novel_document.parse_status = ParseStatus.PROCESSING
                self.current_novel_document.current_step = ParseStep.CHAPTER_SPLIT_COMPLETED
                self.current_novel_document.parse_progress = 30.0
                self.db.commit()
                
                print("✅ 章节分割成功!")
                
                # 显示分割结果
                chapters = self.db.query(NovelChapter).filter(
                    NovelChapter.novel_document_id == self.current_novel_document.id
                ).order_by(NovelChapter.chapter_index).all()
                
                print(f"📊 分割结果: {len(chapters)} 个章节")
                
                return True
            else:
                self.current_novel_document.parse_status = ParseStatus.FAILED
                self.current_novel_document.parse_error = "章节分割失败"
                self.db.commit()
                
                print("❌ 章节分割失败!")
                return False
                
        except Exception as e:
            print(f"❌ 分割执行失败: {e}")
            logger.exception("章节分割异常")
            
            if self.current_novel_document:
                self.current_novel_document.parse_status = ParseStatus.FAILED
                self.current_novel_document.parse_error = str(e)
                self.db.commit()
            
            return False
    
    def show_parse_progress(self):
        """显示解析进度"""
        print("📈 解析进度:")
        
        if not self.current_novel_document:
            print("❌ 没有当前解析任务")
            return
        
        # 刷新数据
        self.db.refresh(self.current_novel_document)
        
        print(f"  - 小说: {self.current_novel_document.title}")
        print(f"  - 状态: {self.current_novel_document.parse_status}")
        print(f"  - 进度: {self.current_novel_document.parse_progress}%")
        print(f"  - 当前步骤: {self.current_novel_document.current_step}")
        
        if self.current_novel_document.parse_error:
            print(f"  - 错误: {self.current_novel_document.parse_error}")
    
    def show_chapter_results(self):
        """显示章节结果"""
        print("📖 章节分割结果:")
        
        if not self.current_novel_document:
            print("❌ 没有当前解析任务")
            return
        
        chapters = self.db.query(NovelChapter).filter(
            NovelChapter.novel_document_id == self.current_novel_document.id
        ).order_by(NovelChapter.chapter_index).all()
        
        if not chapters:
            print("  没有章节数据")
            return
        
        print(f"  总章节数: {len(chapters)}")
        
        total_chars = 0
        for chapter in chapters:
            char_count = len(chapter.content) if chapter.content else 0
            total_chars += char_count
            print(f"  {chapter.chapter_index:3d}. {chapter.title}")
            print(f"       字数: {char_count:,}")
        
        print(f"\n📊 统计信息:")
        print(f"  - 总字数: {total_chars:,}")
        print(f"  - 平均章节字数: {total_chars // len(chapters):,}")
    
    def test_chapter_recognition(self):
        """测试章节识别"""
        print("🔍 测试章节识别...")
        
        if not self.current_novel_document:
            print("❌ 请先创建解析任务")
            return
        
        # 获取文件内容的前2000字符进行测试
        uploaded_file = self.novel_uploaded_dir / self.current_upload_session.filename
        content = uploaded_file.read_text(encoding='utf-8')
        test_content = content[:2000]
        
        print(f"📄 测试内容 (前2000字符):")
        print("-" * 60)
        print(test_content)
        print("-" * 60)
        
        # 简单的章节识别分析
        print("🔍 章节识别分析:")
        lines = test_content.split('\n')
        potential_chapters = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if line and (
                '第' in line and '章' in line or
                'Chapter' in line or
                line in ['序章', '楔子', '前言', '后记', '尾声']
            ):
                potential_chapters.append((i+1, line))
        
        if potential_chapters:
            print("  可能的章节标题:")
            for line_num, title in potential_chapters:
                print(f"    行 {line_num:3d}: {title}")
        else:
            print("  未发现明显的章节标题")
    
    def show_split_rules(self):
        """显示分割规则"""
        print("📋 章节分割规则:")
        
        if not self.current_novel_document:
            print("❌ 没有当前解析任务")
            return
        
        rules = self.db.query(ChapterRegexPattern).filter(
            ChapterRegexPattern.novel_document_id == self.current_novel_document.id
        ).all()
        
        if not rules:
            print("  没有分割规则")
            return
        
        for rule in rules:
            print(f"  规则 {rule.id}:")
            print(f"    类型: {rule.pattern_type}")
            print(f"    正则: {rule.regex_pattern}")
            print(f"    创建时间: {rule.created_at}")
            print()
    
    def export_chapters(self):
        """导出章节文件"""
        print("📤 导出章节文件...")
        
        if not self.current_novel_document:
            print("❌ 没有当前解析任务")
            return
        
        chapters = self.db.query(NovelChapter).filter(
            NovelChapter.novel_document_id == self.current_novel_document.id
        ).order_by(NovelChapter.chapter_index).all()
        
        if not chapters:
            print("❌ 没有章节数据")
            return
        
        # 创建导出目录
        export_dir = self.novel_processed_dir / f"novel_{self.current_novel_document.id}_chapters"
        export_dir.mkdir(exist_ok=True)
        
        try:
            for chapter in chapters:
                filename = f"{chapter.chapter_index:03d}_{chapter.title}.txt"
                # 清理文件名中的非法字符
                filename = "".join(c for c in filename if c.isalnum() or c in "._- ")
                
                file_path = export_dir / filename
                file_path.write_text(chapter.content, encoding='utf-8')
            
            print(f"✅ 章节导出成功:")
            print(f"  - 导出目录: {export_dir}")
            print(f"  - 章节数量: {len(chapters)}")
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")
    
    def re_split_chapters(self):
        """重新分割章节"""
        print("🔄 重新分割章节...")
        
        if not self.current_novel_document:
            print("❌ 没有当前解析任务")
            return
        
        confirm = input("确认重新分割章节? 这将删除现有章节数据 (y/N): ").strip().lower()
        if confirm != 'y':
            print("取消重新分割")
            return
        
        try:
            # 删除现有章节数据
            deleted = self.db.query(NovelChapter).filter(
                NovelChapter.novel_document_id == self.current_novel_document.id
            ).delete()
            
            # 删除分割规则
            self.db.query(ChapterRegexPattern).filter(
                ChapterRegexPattern.novel_document_id == self.current_novel_document.id
            ).delete()
            
            self.db.commit()
            
            print(f"✅ 已删除 {deleted} 个章节")
            
            # 重新执行分割
            self.execute_chapter_split()
            
        except Exception as e:
            print(f"❌ 重新分割失败: {e}")
            self.db.rollback()
    
    def show_main_menu(self):
        """显示主菜单"""
        print("\n" + "="*60)
        print("🧪 小说解析测试框架 (真实文件 + 项目数据库)")
        print("="*60)
        
        # 显示当前状态
        if self.current_novel_file:
            print(f"📖 当前文件: {self.current_novel_file.name}")
        if self.current_upload_session:
            print(f"📤 上传会话: {self.current_upload_session.id}")
        if self.current_novel_document:
            print(f"📋 解析任务: {self.current_novel_document.id} - {self.current_novel_document.title}")
        
        print()
        for key, category in self.available_operations.items():
            print(f"{key}. {category['name']}")
        
        print("0. 退出")
        print("-"*60)
    
    def show_category_menu(self, category_key: str):
        """显示分类菜单"""
        category = self.available_operations[category_key]
        print(f"\n📋 {category['name']}")
        print("-"*40)
        
        for key, (name, _) in category["operations"].items():
            print(f"{key}. {name}")
        
        print("0. 返回主菜单")
        print("-"*40)
    
    def run(self):
        """运行测试框架"""
        print("🚀 启动小说解析测试框架...")
        print(f"📁 小说源目录: {self.novel_source_dir.absolute()}")
        print(f"📁 上传目录: {self.novel_uploaded_dir.absolute()}")
        print(f"📁 处理目录: {self.novel_processed_dir.absolute()}")
        print(f"🗄️  使用项目数据库: {settings.DATABASE_URL}")
        
        while True:
            self.show_main_menu()
            
            try:
                choice = input("请选择功能分类 (0-4): ").strip()
                
                if choice == "0":
                    print("👋 退出测试框架")
                    break
                
                if choice in self.available_operations:
                    while True:
                        self.show_category_menu(choice)
                        sub_choice = input("请选择操作 (0返回): ").strip()
                        
                        if sub_choice == "0":
                            break
                        
                        category = self.available_operations[choice]
                        if sub_choice in category["operations"]:
                            name, func = category["operations"][sub_choice]
                            print(f"\n🔄 执行: {name}")
                            print("-"*40)
                            func()
                        else:
                            print("❌ 无效选择")
                        
                        input("\n按回车键继续...")
                else:
                    print("❌ 无效选择，请重新输入")
                
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出测试框架")
                break
            except Exception as e:
                print(f"❌ 执行错误: {e}")
                logger.exception("测试框架异常")
                input("\n按回车键继续...")
        
        # 清理资源
        if self.db:
            self.db.close()


def main():
    """主函数"""
    framework = NovelParseTestFramework()
    framework.run()


if __name__ == "__main__":
    main()
