# Environment variables
# 不忽略.env和.env.*文件
# .env.*
!.env.example

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo
*.sublime-workspace
*.sublime-project

# Logs
*.log
logs/
log/

# Database
*.sqlite3
*.db
alembic/versions/*.py
!alembic/versions/.gitkeep

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
.coverage.*
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# System files
.DS_Store
Thumbs.db
desktop.ini
*.tmp
*.bak
*.swp
*~

# Local development
local_settings.py
*.local
local.py

# Dependencies
node_modules/
package-lock.json
yarn.lock

# Compiled files
*.pyc
*.pyo
*.pyd
*.so
*.dll
*.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
# 不忽略.env和.env.*文件
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# 允许编辑开发环境配置
!.env.development 